@import url("https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&amp;family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;display=swap");
/*============================================================
[Master Stylesheet]
Theme Name:            Digiboard - Admin & Dashboard HTML Template
Frontend Developer:    www.facebook.com/s.k.a.darda (Shaikh <PERSON>)
Version:               1.0
Author:                Codebasket
URL:                   https://themeforest.net/user/codebasket
=========================================================*/
/*=======================================================
TABLE OF CONTENTS:

01  animation Style
02  chart Style
03  global Style

======section stylsheet======
04  Header section
05  Right Sidebar section
06  Left Sidebar section
07  Main Content section

======Responsive stylsheet======
08  Responsive*/
@-webkit-keyframes fadeUp {
  0% {
    top: 45px;
  }
  100% {
    top: 35px;
  }
}
@keyframes fadeUp {
  0% {
    top: 45px;
  }
  100% {
    top: 35px;
  }
}
@-webkit-keyframes dropdown {
  0% {
    margin-top: 10px;
  }
  100% {
    margin-top: 0;
  }
}
@keyframes dropdown {
  0% {
    margin-top: 10px;
  }
  100% {
    margin-top: 0;
  }
}
@-webkit-keyframes opacity {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes opacity {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@-webkit-keyframes rightBar {
  0% {
    -webkit-transform: translateX(100px);
            transform: translateX(100px);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@keyframes rightBar {
  0% {
    -webkit-transform: translateX(100px);
            transform: translateX(100px);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@keyframes spin {
  0% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
@-webkit-keyframes wobble {
  0% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }
  15% {
    -webkit-transform: rotate(5deg);
            transform: rotate(5deg);
  }
  30% {
    -webkit-transform: rotate(-5deg);
            transform: rotate(-5deg);
  }
  45% {
    -webkit-transform: rotate(4deg);
            transform: rotate(4deg);
  }
  60% {
    -webkit-transform: rotate(-4deg);
            transform: rotate(-4deg);
  }
  75% {
    -webkit-transform: rotate(2deg);
            transform: rotate(2deg);
  }
  85% {
    -webkit-transform: rotate(-2deg);
            transform: rotate(-2deg);
  }
  92% {
    -webkit-transform: rotate(1deg);
            transform: rotate(1deg);
  }
  100% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }
}
@keyframes wobble {
  0% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }
  15% {
    -webkit-transform: rotate(5deg);
            transform: rotate(5deg);
  }
  30% {
    -webkit-transform: rotate(-5deg);
            transform: rotate(-5deg);
  }
  45% {
    -webkit-transform: rotate(4deg);
            transform: rotate(4deg);
  }
  60% {
    -webkit-transform: rotate(-4deg);
            transform: rotate(-4deg);
  }
  75% {
    -webkit-transform: rotate(2deg);
            transform: rotate(2deg);
  }
  85% {
    -webkit-transform: rotate(-2deg);
            transform: rotate(-2deg);
  }
  92% {
    -webkit-transform: rotate(1deg);
            transform: rotate(1deg);
  }
  100% {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }
}
@-webkit-keyframes slideIn {
  0% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    opacity: 1;
  }
}
@keyframes slideIn {
  0% {
    -webkit-transform: translateY(-20px);
            transform: translateY(-20px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    opacity: 1;
  }
}
@-webkit-keyframes grow {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0;
  }
  60% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
}
@keyframes grow {
  0% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 0;
  }
  60% {
    -webkit-transform: scale(1.1);
            transform: scale(1.1);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
}
@-webkit-keyframes dash {
  from {
    stroke-dashoffset: 1000;
    opacity: 0;
  }
  to {
    stroke-dashoffset: 0;
  }
}
@keyframes dash {
  from {
    stroke-dashoffset: 1000;
    opacity: 0;
  }
  to {
    stroke-dashoffset: 0;
  }
}
.apexcharts-datalabel-value,
.apexcharts-legend-text,
.apexcharts-text,
.apexcharts-xaxis-texts-g > *,
.apexcharts-yaxis-texts-g > * {
  fill: #c8d4f0;
  color: #c8d4f0 !important;
}

.apexcharts-pie .apexcharts-text {
  fill: #fff;
}

.apexcharts-datalabel-label {
  fill: #fff;
}

.apexcharts-marker {
  stroke-width: 1;
}

.apexcharts-pie circle, .apexcharts-pie line,
.apexcharts-radar-series line, .apexcharts-radar-series polygon {
  stroke: rgba(255, 255, 255, 0.15);
}

.panel.chart-panel-1 .panel-body {
  padding-right: 10px;
  padding-left: 2px;
  padding-bottom: 1px;
  padding-top: 1px;
}

.panel.chart-panel-2 .panel-body {
  padding-right: 0;
  padding-bottom: 5px;
  padding-top: 1px;
}

.dark-theme .apexcharts-datalabel-value,
.dark-theme .apexcharts-legend-text,
.dark-theme .apexcharts-text,
.dark-theme .apexcharts-xaxis-texts-g > *,
.dark-theme .apexcharts-yaxis-texts-g > * {
  fill: #A9B4CC;
  color: #A9B4CC !important;
}
.dark-theme .apexcharts-gridline {
  stroke: rgba(255, 255, 255, 0.12);
}

.light-theme .apexcharts-datalabel-value,
.light-theme .apexcharts-legend-text,
.light-theme .apexcharts-text,
.light-theme .apexcharts-xaxis-texts-g > *,
.light-theme .apexcharts-yaxis-texts-g > * {
  fill: #595959;
  color: #595959 !important;
}
.light-theme .apexcharts-gridline {
  stroke: rgba(0, 0, 0, 0.17);
}

body {
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  background-color: #071739;
  font-family: "Poppins", sans-serif;
  color: #c8d4f0;
  position: relative;
  z-index: 2;
}
body::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(7, 23, 57, 0.8);
  z-index: -1;
}
body.dark-theme {
  background-color: #18191A;
  color: #A9B4CC;
}
body.dark-theme::after {
  background: rgba(24, 25, 26, 0.8);
}
body.light-theme {
  background-color: #f5f5f5;
  color: #464646;
}
body.light-theme::after {
  background: rgba(245, 245, 245, 0.7);
}

.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f1f4fb;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  z-index: 9999999;
}
.preloader .loader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}
.preloader .loader span {
  width: 15px;
  height: 15px;
  background: #c8d4f0;
  border-radius: 50%;
}
.preloader .loader span:nth-child(1) {
  -webkit-animation: loader 1s infinite alternate;
          animation: loader 1s infinite alternate;
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}
.preloader .loader span:nth-child(2) {
  -webkit-animation: loader 1s infinite alternate;
          animation: loader 1s infinite alternate;
  -webkit-animation-delay: 0.5s;
          animation-delay: 0.5s;
}
.preloader .loader span:nth-child(3) {
  -webkit-animation: loader 1s infinite alternate;
          animation: loader 1s infinite alternate;
  -webkit-animation-delay: 1s;
          animation-delay: 1s;
}

.body-p-top {
  padding-top: 70px;
}

.has-horizontal.body-p-top {
  padding-top: 120px;
}

img {
  max-width: 100%;
}

a {
  display: inline-block;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  text-decoration: none;
}

code {
  color: #ff7d95;
  font-size: 12px;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.form-control[type=file]::-webkit-file-upload-button {
  color: #c8d4f0;
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.form-control[type=file]::file-selector-button {
  color: #c8d4f0;
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.form-control[type=file]:hover::-webkit-file-upload-button {
  background-color: #071739 !important;
}

.form-control[type=file]:hover::file-selector-button {
  background-color: #071739 !important;
}

.divider {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.divider-dash {
  width: 100%;
  border-top: 1px dashed rgba(255, 255, 255, 0.2);
}

label {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

input {
  background: transparent;
}

input[type=radio],
input[type=checkbox] {
  background-color: #071739;
  cursor: pointer;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.dataTables_empty {
  text-align: center !important;
}

.dataTables_wrapper .dataTables_filter {
  margin-bottom: 15px;
}
.dataTables_wrapper .dataTables_filter input {
  border-color: rgba(255, 255, 255, 0.15);
}
.dataTables_wrapper .dataTables_length {
  margin-bottom: 15px;
}
.dataTables_wrapper .dataTables_length label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  color: #c8d4f0;
  font-size: 14px;
}
.dataTables_wrapper .dataTables_info {
  padding-top: 15px;
  font-size: 14px;
  line-height: 20px;
  color: #c8d4f0;
}
.dataTables_wrapper .dataTables_paginate {
  padding-top: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}
.dataTables_wrapper .dataTables_paginate span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}
.dataTables_wrapper .dataTables_paginate a {
  padding: 0;
  width: 20px;
  height: 20px;
  line-height: 18px;
  font-size: 12px;
}

.dataTables_paginate .ellipsis {
  padding: 0 !important;
  color: #c8d4f0;
}

.dataTables_scrollHeadInner {
  width: 100% !important;
  padding: 0 !important;
}

.dataTables_scrollBody {
  border-bottom: 0 !important;
}
.dataTables_scrollBody::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.dataTables_scrollBody::-webkit-scrollbar-track {
  background: #112143;
}
.dataTables_scrollBody::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}
.dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

.select2.select2-container {
  display: block;
  width: 100% !important;
}
.select2.select2-container .form-control {
  padding-right: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #647aab;
  font-size: 14px;
}

.select2-dropdown {
  min-width: -webkit-max-content !important;
  min-width: -moz-max-content !important;
  min-width: max-content !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  background: #071739;
  border-radius: 3px !important;
  overflow: hidden;
  margin-top: 2px;
}
.select2-dropdown.select2-in-modal {
  z-index: 1055;
}

.select2-search__field {
  color: #464646 !important;
}

.select2-results__options {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}
.select2-results__options::-webkit-scrollbar {
  width: 5px;
}
.select2-results__options::-webkit-scrollbar-track {
  background: transparent;
}
.select2-results__options::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 5px;
}

.select2-results__option {
  font-size: 14px;
  padding: 5px 10px;
}

.select2-container .select2-selection--single {
  height: auto;
}

.select2-container--default .select2-selection--single {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  min-width: 23px;
  line-height: 1.5;
  color: #c8d4f0;
  padding: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: calc(100% - 2px);
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  border-color: #c8d4f0 transparent transparent transparent;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent #c8d4f0 transparent;
}

.select2-container--default .select2-results__option[aria-selected=true] {
  background: rgba(255, 255, 255, 0.15);
}

.select2-container--default .select2-selection--multiple {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: transparent;
  border-color: rgba(255, 255, 255, 0.15);
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: #c8d4f0;
  font-weight: 400;
  margin-right: 5px;
  margin-bottom: -1px;
  font-size: 16px;
  display: inline-block;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 5px;
  padding: 0;
  height: 100%;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #c8d4f0;
  border: 0;
  border-radius: 3px;
  height: 20px;
  font-size: 13px;
  font-weight: 300;
  line-height: 20px;
  letter-spacing: 0.5px;
  padding: 0 10px;
  margin: 0;
}

.select2-container .select2-search--inline .select2-search__field {
  height: 20px;
  margin: 0;
  color: #c8d4f0 !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: rgba(255, 255, 255, 0.1);
  color: #c8d4f0 !important;
}

.daterangepicker {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  border: 0;
  border-radius: 3px;
  color: #595959;
}
.daterangepicker.opensleft::before {
  right: 62.5px;
}
.daterangepicker.opensleft::after {
  right: 63px;
}
.daterangepicker.show-calendar {
  right: 30px !important;
}
.daterangepicker .ranges ul li:first-child {
  border-radius: 2px 2px 0 0;
}
.daterangepicker .ranges ul li:last-child {
  border-radius: 0 0 2px 2px;
}

.dtp > .dtp-content {
  background: #071739;
  max-height: none;
}

.dtp div.dtp-date, .dtp div.dtp-time {
  background: #112143;
}

.dtp-date > * {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: -webkit-max-content;
  height: -moz-max-content;
  height: max-content;
}
.dtp-date > *:first-child > * {
  line-height: 100%;
}
.dtp-date > * .p10 {
  height: 25px;
}

.dtp .p10 > a {
  width: 25px;
  height: 25px;
  line-height: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.dtp div.dtp-actual-year,
.dtp div.dtp-actual-month {
  font-size: 16px;
  font-weight: 400;
  line-height: 25px;
  color: #c8d4f0;
}

.dtp div.dtp-actual-num {
  font-size: 25px;
  height: 35px;
  line-height: 35px;
}

.dtp div.dtp-picker {
  padding: 20px 15px;
}

.dtp div.dtp-picker-month, .dtp div.dtp-actual-time {
  color: #ebebeb;
  font-weight: 400;
  line-height: 100%;
  padding-bottom: 13px !important;
  margin-top: -2px;
}

.dtp table.dtp-picker-days {
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dtp table.dtp-picker-days tr > th {
  border-color: rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
  color: #fff;
  font-weight: 300;
  padding: 2px;
}

.dtp table.dtp-picker-days tr > td {
  padding: 0;
  vertical-align: middle;
}
.dtp table.dtp-picker-days tr > td span, .dtp table.dtp-picker-days tr > td a {
  padding: 0;
  width: 30px;
  height: 30px;
  line-height: 30px;
  color: #c4c4c4;
  font-family: "Lato", sans-serif;
  font-weight: 400;
  font-size: 12px;
}
.dtp table.dtp-picker-days tr > td span.dtp-select-day {
  color: #8b97b2 !important;
  opacity: 0.5;
}

.dtp a.dtp-meridien-am, .dtp a.dtp-meridien-pm {
  width: 40px;
  height: 40px;
  line-height: 40px;
  padding: 0;
}

#dtp-svg-clock circle:first-child {
  fill: #112143;
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 1px;
}
#dtp-svg-clock text {
  fill: #97a3bb;
  font-weight: 400;
}
#dtp-svg-clock text.dtp-select-hour-text.current {
  fill: #fff;
}

.dtp-select-minute,
.dtp-select-hour {
  width: 35px;
  height: 35px;
}
.dtp-select-minute[r="3"],
.dtp-select-hour[r="3"] {
  fill: rgba(255, 255, 255, 0.05);
}

.dtp-select-minute-text[fill="#fff"],
.dtp-select-hour-text[fill="#fff"] {
  fill: #fff !important;
}

.dtp-buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  gap: 5px;
}
.dtp-buttons .clearfix {
  display: none;
}

.year-picker-item {
  color: #8b97b2;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.dtp-select-year-range {
  color: #fff;
  border: 0 !important;
}
.dtp-select-year-range:hover {
  color: #fff;
}

.selectize-input {
  padding: 0;
  border: 0;
  background: transparent;
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
}

.selectize-control.multi .selectize-input.has-items {
  padding: 0;
}

.selectize-input > input {
  font-size: 14px;
  width: 100% !important;
}

.file-delete,
.progress-loading {
  color: #fff;
}

.ui-datepicker {
  background: #071739;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  padding: 3px;
}
.ui-datepicker .ui-datepicker-header {
  background: #112143;
  border: 0;
  color: #c8d4f0;
  font-weight: 400;
}
.ui-datepicker .ui-datepicker-prev span {
  background: none;
  position: relative;
  cursor: pointer;
}
.ui-datepicker .ui-datepicker-prev span::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
          transform: translate(-50%, -50%) rotate(45deg);
  width: 12px;
  height: 12px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
  border-bottom-left-radius: 3px;
  opacity: 0.5;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.ui-datepicker .ui-datepicker-prev span:hover::after {
  opacity: 1;
}
.ui-datepicker .ui-datepicker-prev-hover {
  top: 2px;
  left: 2px;
  background: transparent;
  border: 0;
}
.ui-datepicker .ui-datepicker-next span {
  background: none;
  position: relative;
  cursor: pointer;
}
.ui-datepicker .ui-datepicker-next span::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
          transform: translate(-50%, -50%) rotate(-45deg);
  width: 12px;
  height: 12px;
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
  border-bottom-right-radius: 3px;
  opacity: 0.5;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.ui-datepicker .ui-datepicker-next span:hover::after {
  opacity: 1;
}
.ui-datepicker .ui-datepicker-next-hover {
  top: 2px;
  right: 2px;
  background: transparent;
  border: 0;
}
.ui-datepicker table {
  margin: 0;
}
.ui-datepicker th {
  font-weight: 400;
  color: #c8d4f0;
  padding: 5px 10px;
}
.ui-datepicker td {
  width: 35px;
  height: 35px;
  padding: 0;
  border: 1px solid rgba(255, 255, 255, 0.07);
}
.ui-datepicker td a {
  height: 100%;
  border: 0 !important;
}
.ui-datepicker td a.ui-state-default {
  background: #112143;
  color: #c8d4f0;
}
.ui-datepicker td a.ui-state-highlight {
  color: #fff;
}
.ui-datepicker td a.ui-priority-secondary {
  background: #071739;
}
.ui-datepicker td a.ui-state-active {
  background: rgba(255, 255, 255, 0.15);
}

.fc-toolbar.fc-header-toolbar {
  margin-bottom: 20px;
}

.fc-toolbar h2 {
  font-size: 20px;
  line-height: 100%;
  color: #fff;
}

.fc-other-month {
  background: #071739;
}

.fc-unthemed td {
  border-color: rgba(255, 255, 255, 0.15);
  border-style: dashed;
}

.fc-unthemed td.fc-today {
  background: rgba(255, 255, 255, 0.1);
  border-style: dashed;
}

.fc-dayGrid-view .fc-day-number, .fc-dayGrid-view .fc-week-number {
  padding: 5px;
}

.fc-unthemed th {
  border-color: rgba(255, 255, 255, 0.15);
  background: rgba(255, 255, 255, 0.15);
  padding: 10px;
}

.fc-event, .fc-event-dot {
  border: 0;
  margin-bottom: 10px;
  padding: 3px 5px;
  border-radius: 2px;
  cursor: move;
  font-size: 13px;
}

.fc-day-grid-event {
  margin: 0 5px;
  margin-bottom: 5px;
}

.swal2-popup {
  background-color: #112143;
  padding: 15px;
}
.swal2-popup .swal2-title {
  font-size: 20px;
  line-height: 100%;
  font-weight: 500;
  color: #c8d4f0;
}
.swal2-popup .swal2-html-container {
  color: #c8d4f0;
  font-size: 16px;
}
.swal2-popup .swal2-actions {
  gap: 10px;
}
.swal2-popup .swal2-footer {
  border-color: rgba(255, 255, 255, 0.1);
}
.swal2-popup a {
  color: #c8d4f0;
  text-decoration: underline;
}
.swal2-popup input {
  margin-left: 0;
  margin-right: 0;
  height: auto;
}

.nested-sortable-handle,
.nested-sortable {
  gap: 5px;
  margin-top: 8px;
}
.nested-sortable-handle:not(.nested-sortable-handle) .list-group-item,
.nested-sortable:not(.nested-sortable-handle) .list-group-item {
  cursor: all-scroll;
}
.nested-sortable-handle .handle,
.nested-sortable .handle {
  cursor: all-scroll;
  margin-right: 5px;
}

.swiper * {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.swiper-button-next, .swiper-button-prev {
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  margin: 0;
}
.swiper-button-next::after, .swiper-button-prev::after {
  display: none;
}

.swiper-pagination-bullet {
  min-width: 20px;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
  font-weight: 600;
  color: #fff;
  opacity: 0.7;
}
.swiper-pagination-bullet:empty {
  min-width: 10px;
  min-height: 10px;
}
.swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  min-width: auto;
  min-height: auto;
  width: 10px;
  height: 10px;
  border-radius: 3px;
  opacity: 1;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet.swiper-pagination-bullet-active {
  width: 40px;
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev,
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev,
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
}

.swiper-pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 100% !important;
}
.swiper-pagination.swiper-pagination-vertical {
  width: auto !important;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.swiper-pagination.swiper-pagination-vertical .swiper-pagination-bullet {
  width: 25px;
  height: 5px;
  min-height: auto;
  border-radius: 3px;
  background: #fff;
  margin: 2px 0;
  opacity: 1;
}

.swiper-pagination-fraction {
  color: #fff;
  background: rgba(0, 0, 0, 0.3);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
}

.row {
  --bs-gutter-y: 25px;
  --bs-gutter-x: 25px;
}

.g-3, .gy-3 {
  --bs-gutter-y: 20px;
}

.g-3, .gx-3 {
  --bs-gutter-x: 20px;
}

.g-2, .gy-2 {
  --bs-gutter-y: 10px;
}

.g-2, .gx-2 {
  --bs-gutter-x: 10px;
}

.g-1, .gy-1 {
  --bs-gutter-y: 5px;
}

.g-1, .gx-1 {
  --bs-gutter-x: 5px;
}

.g-0, .gy-0 {
  --bs-gutter-y: 0;
}

.g-0, .gx-0 {
  --bs-gutter-x: 0;
}

@media screen and (min-width: 1400px) {
  .g-xxl-3, .gy-xxl-3 {
    --bs-gutter-y: 20px;
  }
  .g-xxl-3, .gx-xxl-3 {
    --bs-gutter-x: 20px;
  }
  .g-xxl-2, .gy-xxl-2 {
    --bs-gutter-y: 10px;
  }
  .g-xxl-2, .gx-xxl-2 {
    --bs-gutter-x: 10px;
  }
  .g-xxl-1, .gy-xxl-1 {
    --bs-gutter-y: 5px;
  }
  .g-xxl-1, .gx-xxl-1 {
    --bs-gutter-x: 5px;
  }
  .g-xxl-0, .gy-xxl-0 {
    --bs-gutter-y: 0;
  }
  .g-xxl-0, .gx-xxl-0 {
    --bs-gutter-x: 0;
  }
}
@media screen and (min-width: 1200px) {
  .g-xl-3, .gy-xl-3 {
    --bs-gutter-y: 20px;
  }
  .g-xl-3, .gx-xl-3 {
    --bs-gutter-x: 20px;
  }
  .g-xl-2, .gy-xl-2 {
    --bs-gutter-y: 10px;
  }
  .g-xl-2, .gx-xl-2 {
    --bs-gutter-x: 10px;
  }
  .g-xl-1, .gy-xl-1 {
    --bs-gutter-y: 5px;
  }
  .g-xl-1, .gx-xl-1 {
    --bs-gutter-x: 5px;
  }
  .g-xl-0, .gy-xl-0 {
    --bs-gutter-y: 0;
  }
  .g-xl-0, .gx-xl-0 {
    --bs-gutter-x: 0;
  }
}
@media screen and (min-width: 992px) {
  .g-lg-3, .gy-lg-3 {
    --bs-gutter-y: 20px;
  }
  .g-lg-3, .gx-lg-3 {
    --bs-gutter-x: 20px;
  }
  .g-lg-2, .gy-lg-2 {
    --bs-gutter-y: 10px;
  }
  .g-lg-2, .gx-lg-2 {
    --bs-gutter-x: 10px;
  }
  .g-lg-1, .gy-lg-1 {
    --bs-gutter-y: 5px;
  }
  .g-lg-1, .gx-lg-1 {
    --bs-gutter-x: 5px;
  }
  .g-lg-0, .gy-lg-0 {
    --bs-gutter-y: 0;
  }
  .g-lg-0, .gx-lg-0 {
    --bs-gutter-x: 0;
  }
}
.btn {
  padding: 4.5px 10px;
  border-radius: 3px;
}
.btn.btn-icon {
  padding: 0;
  width: 35px;
  height: 35px;
  line-height: 35px;
}

.btn-sm.btn-icon {
  padding: 0;
  width: 31px;
  height: 31px;
  line-height: 31px;
}

.btn-outline-primary {
  color: #e4e4e4;
}

.btn-secondary:focus, .btn-secondary:active, .btn-outline-secondary:focus, .btn-outline-secondary:active {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.3);
          box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.3);
}

.btn-outline-secondary {
  color: #c4c4c4;
}

.btn-success:focus, .btn-success:active, .btn-outline-success:focus, .btn-outline-success:active {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(45, 137, 58, 0.3);
          box-shadow: 0 0 0 0.25rem rgba(45, 137, 58, 0.3);
}

.btn-danger:focus, .btn-danger:active, .btn-outline-danger:focus, .btn-outline-danger:active {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(240, 0, 0, 0.3);
          box-shadow: 0 0 0 0.25rem rgba(240, 0, 0, 0.3);
}

.btn-warning:focus, .btn-warning:active, .btn-outline-warning:focus, .btn-outline-warning:active {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(216, 162, 0, 0.3);
          box-shadow: 0 0 0 0.25rem rgba(216, 162, 0, 0.3);
}

.btn-info:focus, .btn-info:active, .btn-outline-info:focus, .btn-outline-info:active {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(95, 203, 250, 0.3);
          box-shadow: 0 0 0 0.25rem rgba(95, 203, 250, 0.3);
}

.btn-dark:focus, .btn-dark:active, .btn-outline-dark:focus, .btn-outline-dark:active {
  -webkit-box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.3);
          box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.3);
}

.btn-group-sm > .btn, .btn-sm {
  padding: 3.5px 8px;
  --bs-btn-border-radius: 3px;
}

.badge {
  border-radius: 15px;
  padding: 6px 10px;
  font-weight: 400;
}

.text-muted {
  color: #c8d4f0 !important;
  opacity: 0.7;
  font-size: 13px;
}

.form-label {
  display: block;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  font-size: 14px;
  line-height: 100%;
  font-weight: 400;
  color: #c8d4f0;
  margin-top: -2px;
  margin-bottom: 13px;
}

.col-form-label {
  color: #c8d4f0;
  height: -webkit-max-content;
  height: -moz-max-content;
  height: max-content;
}

.form-select,
.form-control {
  background-color: transparent;
  border: 1px solid rgba(223, 223, 223, 0.15);
  border-radius: 3px;
  color: #A9B4CC;
  font-size: 14px;
}
.form-select:focus,
.form-control:focus {
  background-color: transparent;
  color: #c8d4f0;
}
.form-select::-webkit-input-placeholder, .form-control::-webkit-input-placeholder {
  color: #647aab;
  font-size: 13px;
}
.form-select::-moz-placeholder, .form-control::-moz-placeholder {
  color: #647aab;
  font-size: 13px;
}
.form-select:-ms-input-placeholder, .form-control:-ms-input-placeholder {
  color: #647aab;
  font-size: 13px;
}
.form-select::-ms-input-placeholder, .form-control::-ms-input-placeholder {
  color: #647aab;
  font-size: 13px;
}
.form-select::placeholder,
.form-control::placeholder {
  color: #647aab;
  font-size: 13px;
}

.form-select {
  background-image: url(../images/arrow-down.png);
  background-size: 12px;
  background-position: right 10px center;
  background-repeat: no-repeat;
}

.form-control:disabled {
  background: rgba(255, 255, 255, 0.15);
}

.form-control-plaintext {
  color: #fff;
  opacity: 0.8;
  font-size: 14px;
}

.input-group > *:first-child {
  border-radius: 3px 0 0 3px;
  border-right-width: 0;
}
.input-group > *:last-child {
  border-radius: 0 3px 3px 0;
}

.input-group-text {
  min-width: 40px;
  padding: 0 5px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border: 1px solid rgba(223, 223, 223, 0.15);
  border-radius: 3px;
  background: transparent;
  color: #c8d4f0;
  font-size: 14px;
}

.form-check {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-height: auto;
  margin: 0;
  padding: 0;
}
.form-check:not(.form-switch) .form-check-input[type=checkbox] {
  border-radius: 3px;
}
.form-check:not(.form-switch) .form-check-input[type=checkbox]:checked {
  background-image: url(../images/check-mark.png);
  background-size: 13px;
}
.form-check:not(.form-switch) .form-check-input[type=radio]:checked {
  background-image: url(../images/circle-1.png);
  background-size: 8px;
}
.form-check:not(.form-switch) .form-check-input {
  width: 16px;
  height: 16px;
  margin: 0;
  cursor: pointer;
  background: transparent;
  background-position: center center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.form-check:not(.form-switch) .form-check-input:checked {
  background: rgba(255, 255, 255, 0.2);
  background-position: center center;
  background-repeat: no-repeat;
}
.form-check label {
  font-size: 14px;
  line-height: 16px;
  padding-left: 10px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  cursor: pointer;
  color: #c8d4f0;
}
.form-check.form-switch .form-check-input {
  background-color: transparent;
  --bs-form-switch-bg: url(../../images/circle-1.html);
  background-size: 16px;
  background-position-x: 4px;
  height: 24px;
  width: 50px;
  margin: 0;
  cursor: pointer;
}
.form-check.form-switch .form-check-input:checked {
  --bs-form-switch-bg: url(../../images/circle-2.html);
  background-position-x: 30px;
}

.modal-backdrop {
  z-index: 1052;
}

.modal {
  --bs-modal-inner-border-radius: 3px;
  --bs-modal-border-radius: 3px;
}
.modal .modal-content {
  background: #112143;
}
.modal .modal-header {
  padding: 15px;
  border-color: rgba(255, 255, 255, 0.1);
}
.modal .modal-header .modal-title {
  font-family: "Lato", sans-serif;
  font-size: 20px;
  line-height: 100%;
  font-weight: 500;
  color: #F7F7F7;
  margin-top: -2px;
}
.modal .modal-body {
  padding: 15px;
}
.modal .modal-footer {
  padding: 15px;
  border-color: rgba(255, 255, 255, 0.1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  gap: 10px;
}
.modal .modal-footer .btn {
  margin: 0;
}

.dropdown-divider {
  border-color: rgba(255, 255, 255, 0.1);
  margin: 2px 0;
}

.dropdown-item:focus, .dropdown-item:active {
  background: transparent;
}

.progress {
  height: 5px;
  border-radius: 3px;
}
.progress .progress-bar {
  border-radius: 3px;
}

.progress-stacked .progress {
  border-radius: 0;
}
.progress-stacked .progress .progress-bar {
  border-radius: 0;
}

.table {
  color: #c8d4f0;
  font-size: 14px;
  margin-bottom: 0;
  min-width: -webkit-max-content;
  min-width: -moz-max-content;
  min-width: max-content;
}
.table thead {
  background: rgba(255, 255, 255, 0.1);
}
.table th {
  font-weight: 500;
  color: #e4e4e4;
}
.table th:not(:first-child) {
  text-align: center;
}
.table th:last-child {
  text-align: right;
}
.table th.fc-col-header-cell:last-child {
  text-align: center;
}
.table td {
  border-style: dashed;
  vertical-align: middle;
}
.table td:not(:first-child) {
  text-align: center;
}
.table td:last-child {
  text-align: right;
}
.table tr:hover td {
  color: #FDFDFD;
}
.table tr:focus {
  background-color: rgba(255, 255, 255, 0.05);
}
.table.table-striped > tbody > tr:nth-of-type(odd) > * {
  color: #c8d4f0;
}
.table.table-striped-columns > :not(caption) > tr > :nth-child(2n) {
  color: #c8d4f0;
}
.table.table-success {
  color: #2d893a;
}
.table.table-success tr:hover td {
  color: #2d893a;
}
.table.table-success tr:focus {
  background-color: rgba(255, 255, 255, 0.05);
}
.table.table-bordered tr {
  border-style: dashed;
}

.card {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.15);
}
.card .card-header {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.15);
  padding: 5px 15px;
}
.card .card-body {
  padding: 15px;
}

.list-group-item {
  background-color: rgba(255, 255, 255, 0.03);
  color: #c8d4f0;
  border-width: 1px !important;
  font-size: 14px;
  line-height: 2;
}

:root {
  --bs-border-radius: 3px;
  --bs-border-color: rgba(255, 255, 255, 0.07);
  --bs-primary-bg-subtle: #23365e;
}
:root .light-theme {
  --bs-border-color: rgba(0, 0, 0, 0.1);
  --bs-primary-bg-subtle: #e7f1fe;
}
:root .dark-theme {
  --bs-primary-bg-subtle: #3A3B3C;
}

.scrollable {
  height: 100%;
}
.scrollable .os-scrollbar-vertical {
  padding: 0;
  width: 6px;
}
.scrollable .os-scrollbar-vertical .os-scrollbar-handle {
  background: transparent !important;
}
.scrollable:hover .os-scrollbar-vertical .os-scrollbar-handle {
  background: rgba(255, 255, 255, 0.2) !important;
}
.scrollable:hover .os-scrollbar-vertical .os-scrollbar-handle:hover {
  background: rgba(255, 255, 255, 0.3) !important;
}

.bg-blue-theme {
  background: #112143;
}

.text-fb {
  background: #1877F2 !important;
}

.text-behance {
  background: #0056FF !important;
}

.text-comb {
  background: #FB651E !important;
}

.text-insta {
  background: #F402D1 !important;
}

.text-dribble {
  background: #C42462 !important;
}

.text-pinterest {
  background: #B7081B !important;
}

.text-linkedin {
  background: #0077B5 !important;
}

.text-twitter {
  background: #1DA1F2 !important;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-10 {
  margin-bottom: 10px;
}

.panel-bg {
  background: #112143;
}

.panel {
  background: #112143;
  border-radius: 3px;
}
.panel.collapsed .panel-header {
  border-color: transparent;
}
.panel .panel-header {
  height: 60px;
  padding: 0 20px;
  border-bottom: 1px dashed rgba(223, 223, 223, 0.15);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.panel .panel-header h5 {
  font-family: "Lato", sans-serif;
  font-size: 20px;
  line-height: 60px;
  font-weight: 500;
  color: #F0F0F0;
  margin-bottom: 0;
}
.panel .panel-header form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  padding-right: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.panel .panel-header form input {
  border: 0;
  border-radius: 0;
}
.panel .panel-header form input:focus {
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
}
.panel .panel-header form button {
  border: 0;
  background: transparent;
  padding: 0;
  color: #F9F9F9;
  font-size: 12px;
}
.panel .panel-body {
  padding: 20px;
}

.table-dashed th, .table-dashed td {
  border-style: dashed !important;
}

.gap-20 {
  gap: 20px;
}

.h-100-p {
  height: 100px;
}

.h-150-p {
  height: 150px;
}

.avatar {
  display: inline-block;
  width: 40px;
  height: 40px;
}
.avatar.avatar-lg {
  width: 60px;
  height: 60px;
}
.avatar.avatar-lg img {
  border-radius: 30px;
}
.avatar.avatar-sm {
  width: 30px;
  height: 30px;
}
.avatar.avatar-sm img {
  border-radius: 15px;
}
.avatar img {
  border-radius: 20px;
  width: 100%;
}

.w-max-content {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

.btn-flush {
  font-size: 16px;
  background: transparent;
  border: 0;
  padding: 0;
  color: #e4e4e4;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.input-flush {
  padding: 0;
  border: 0;
  outline: none;
}

.form-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}

.fs-14 {
  font-size: 14px;
}

.input-group-with-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.input-group-with-icon .input-icon {
  width: 35px;
  color: #c8d4f0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.input-group-with-icon input {
  width: 100%;
  height: 35px;
  border: 0;
  outline: none;
  color: #c8d4f0;
  font-size: 14px;
  margin-top: -1px;
  margin-bottom: -1px;
}
.input-group-with-icon input::-webkit-input-placeholder {
  color: #c8d4f0;
  opacity: 0.5;
}
.input-group-with-icon input::-moz-placeholder {
  color: #c8d4f0;
  opacity: 0.5;
}
.input-group-with-icon input:-ms-input-placeholder {
  color: #c8d4f0;
  opacity: 0.5;
}
.input-group-with-icon input::-ms-input-placeholder {
  color: #c8d4f0;
  opacity: 0.5;
}
.input-group-with-icon input::placeholder {
  color: #c8d4f0;
  opacity: 0.5;
}
.input-group-with-icon input:focus {
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
}

.digi-dropdown {
  position: relative;
}
.digi-dropdown .dropdown-menu {
  background: #112143;
  border: 1px solid rgba(255, 255, 255, 0.2);
  -webkit-box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.15);
          box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.15);
  left: 0;
  margin-top: 2px;
  overflow: visible;
  z-index: 13;
}
.digi-dropdown .dropdown-menu.dropdown-slim li {
  padding: 0;
}
.digi-dropdown .dropdown-menu li {
  padding: 10px 10px;
}
.digi-dropdown .dropdown-menu li.dropdown-title {
  font-size: 14px;
  line-height: 100%;
  color: #F9F9F9;
  margin-top: -2px;
  margin-bottom: -2px;
}
.digi-dropdown .dropdown-menu li .dropdown-item {
  color: #c8d4f0;
  text-decoration: none;
}
.digi-dropdown .dropdown-menu li .dropdown-item:hover {
  color: #fff;
}

.table .digi-dropdown .dropdown-menu::after {
  display: none;
}

.btn-star {
  background: transparent;
  padding: 0;
  border: 0;
  color: #A9B4CC;
  margin-right: 15px;
  font-size: 14px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.btn-star.starred {
  color: #F0C434;
}

.btn .badge {
  padding: 0 4px;
  min-width: 17px;
  height: 17px;
  line-height: 17px;
}

.progress-box p {
  font-size: 14px;
  color: #c8d4f0;
  margin-top: -4px;
}

.avatar-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.avatar-box .avatar:not(:first-child) {
  margin-left: -15px;
}

.date-box {
  width: 50px;
  height: 50px;
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: #071739;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.date-box.date-box-lg {
  width: 65px;
  height: 65px;
}
.date-box.date-box-lg span {
  font-size: 16px;
}
.date-box span {
  display: block;
  font-size: 14px;
  line-height: 1.2;
}

::-webkit-calendar-picker-indicator {
  -webkit-filter: invert(1);
          filter: invert(1);
}

.border-dotted {
  border-style: dotted !important;
}

.border-top-dotted {
  border-top-style: dotted !important;
}

.border-end-dotted {
  border-right-style: dotted !important;
}

.border-bottom-dotted {
  border-bottom-style: dotted !important;
}

.border-start-dotted {
  border-left-style: dotted !important;
}

.border-dashed {
  border-style: dashed !important;
}

.border-top-dashed {
  border-top-style: dashed !important;
}

.border-end-dashed {
  border-right-style: dashed !important;
}

.border-bottom-dashed {
  border-bottom-style: dashed !important;
}

.border-start-dashed {
  border-left-style: dashed !important;
}

.border-groove {
  border-style: groove !important;
}

.border-top-groove {
  border-top-style: groove !important;
}

.border-end-groove {
  border-right-style: groove !important;
}

.border-bottom-groove {
  border-bottom-style: groove !important;
}

.border-start-groove {
  border-left-style: groove !important;
}

.border-outset {
  border-style: outset !important;
}

.border-top-outset {
  border-top-style: outset !important;
}

.border-end-outset {
  border-right-style: outset !important;
}

.border-bottom-outset {
  border-bottom-style: outset !important;
}

.border-start-outset {
  border-left-style: outset !important;
}

.border-ridge {
  border-style: ridge !important;
}

.border-top-ridge {
  border-top-style: ridge !important;
}

.border-end-ridge {
  border-right-style: ridge !important;
}

.border-bottom-ridge {
  border-bottom-style: ridge !important;
}

.border-start-ridge {
  border-left-style: ridge !important;
}

.border-inset {
  border-style: inset !important;
}

.border-top-inset {
  border-top-style: inset !important;
}

.border-end-inset {
  border-right-style: inset !important;
}

.border-bottom-inset {
  border-bottom-style: inset !important;
}

.border-start-inset {
  border-left-style: inset !important;
}

.form-floating label {
  color: #c8d4f0;
}

.active-mark {
  display: inline-block;
  text-align: center;
  width: 15px;
  height: 15px;
  line-height: 16px;
  background: #2d893a;
  color: #fff;
  font-size: 10px;
  border-radius: 3px;
}

.dataTable-resize .resize-col {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.digi-dataTable tr:last-child .dropdown-menu::after {
  top: calc(100% - 4px);
  bottom: auto;
  -webkit-transform: rotate(-135deg);
          transform: rotate(-135deg);
}

#tableSearch .form-control {
  max-width: 170px;
}

.table-filter-option .row .col {
  width: auto;
  -webkit-box-flex: 0;
      -ms-flex: none;
          flex: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}
.table-filter-option .row .col .form-control {
  max-width: 170px;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

.table-responsive {
  -webkit-clip-path: inset(-200vw 0vw -200vw 0);
}
.table-responsive::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.table-responsive::-webkit-scrollbar-track {
  background: #112143;
}
.table-responsive::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}
.table-responsive::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

.table .select2-container {
  width: -webkit-max-content !important;
  width: -moz-max-content !important;
  width: max-content !important;
}
.table span.form-control {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

pre {
  margin-bottom: 0;
}
pre .tag {
  font-style: italic;
  color: #d8a200;
}
pre .attr-name {
  color: #5fcbfa;
}
pre .attr-value {
  color: #c8d4f0;
}
pre .punctuation {
  color: #dfdfdf;
  opacity: 0.5;
}

.utility-box {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.03);
  margin: auto;
}

/* ===== Global Style For Dark Theme ===== */
.dark-theme .form-control[type=file]::-webkit-file-upload-button {
  color: #A9B4CC;
  background-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .form-control[type=file]::file-selector-button {
  color: #A9B4CC;
  background-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .form-control[type=file]:hover::-webkit-file-upload-button {
  background-color: #3A3B3C !important;
}
.dark-theme .form-control[type=file]:hover::file-selector-button {
  background-color: #3A3B3C !important;
}
.dark-theme input[type=radio],
.dark-theme input[type=checkbox] {
  background-color: rgba(0, 0, 0, 0.07);
}
.dark-theme .dataTables_scrollBody::-webkit-scrollbar-track {
  background: #3A3B3C;
}
.dark-theme .dataTables_scrollBody::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}
.dark-theme .dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}
.dark-theme .dataTables_wrapper .dataTables_length label {
  color: #A9B4CC;
}
.dark-theme .dataTables_wrapper .dataTables_filter input {
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .dataTables_wrapper .dataTables_info {
  color: #A9B4CC;
}
.dark-theme table.dataTable thead .sorting {
  background-image: url(../vendor/images/sort_both_white.png);
}
.dark-theme table.dataTable thead .sorting_asc {
  background-image: url(../vendor/images/sort_asc_white.png) !important;
}
.dark-theme table.dataTable thead .sorting_desc {
  background-image: url(../vendor/images/sort_desc_white.png) !important;
}
.dark-theme .ui-datepicker {
  background: #242526;
  border-color: rgba(255, 255, 255, 0.1) !important;
}
.dark-theme .ui-datepicker .ui-datepicker-header {
  background: #3A3B3C;
  color: #dfdfdf;
}
.dark-theme .ui-datepicker th {
  color: #dfdfdf;
}
.dark-theme .ui-datepicker td {
  border-color: rgba(0, 0, 0, 0.1);
}
.dark-theme .ui-datepicker .ui-datepicker-prev span::after,
.dark-theme .ui-datepicker .ui-datepicker-next span::after {
  border-color: #dfdfdf;
}
.dark-theme .ui-datepicker td a.ui-state-default:not(.ui-state-highlight) {
  background: #3A3B3C;
  color: #A9B4CC;
}
.dark-theme .ui-datepicker td a.ui-priority-secondary {
  opacity: 0.4;
}
.dark-theme .ui-datepicker td a.ui-state-active {
  background: rgba(255, 255, 255, 0.25) !important;
}
.dark-theme .select2-dropdown {
  background: #242526;
  border-color: rgba(255, 255, 255, 0.1) !important;
}
.dark-theme .select2-results__options {
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}
.dark-theme .select2-results__options::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
}
.dark-theme .select2-container--default .select2-results__option[aria-selected=true] {
  background: #3A3B3C;
  color: #e4e4e4;
}
.dark-theme .select2-container--default .select2-results__option[aria-selected=true]:hover {
  color: #e4e4e4;
}
.dark-theme .select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #c4c4c4;
}
.dark-theme .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background: rgba(255, 255, 255, 0.1);
  color: #e4e4e4;
}
.dark-theme .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: #fff;
}
.dark-theme .select2-container .select2-search--inline .select2-search__field {
  color: #e4e4e4 !important;
}
.dark-theme .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: rgba(255, 255, 255, 0.2);
  color: #A9B4CC !important;
}
.dark-theme .dtp > .dtp-content {
  background: #242526;
}
.dark-theme .dtp div.dtp-date, .dark-theme .dtp div.dtp-time {
  background: #3A3B3C;
}
.dark-theme .dtp .p10 > a {
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .dtp div.dtp-date, .dark-theme .dtp div.dtp-time {
  color: #e4e4e4;
}
.dark-theme .dtp div.dtp-actual-year, .dark-theme .dtp div.dtp-actual-month {
  color: #c4c4c4;
}
.dark-theme .dtp div.dtp-picker-month, .dark-theme .dtp div.dtp-actual-time {
  color: #e4e4e4;
}
.dark-theme .year-picker-item {
  color: #A9B4CC;
}
.dark-theme .dtp table.dtp-picker-days {
  border-color: rgba(255, 255, 255, 0.1);
}
.dark-theme .dtp table.dtp-picker-days tr > th {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: #e4e4e4;
}
.dark-theme .dtp table.dtp-picker-days tr > td span, .dark-theme .dtp table.dtp-picker-days tr > td a {
  color: #A9B4CC;
}
.dark-theme .dtp table.dtp-picker-days tr > td span.dtp-select-day {
  color: #A9B4CC !important;
  opacity: 0.45;
}
.dark-theme .dtp table.dtp-picker-days tr > td > a.selected {
  color: #fff;
}
.dark-theme #dtp-svg-clock circle:first-child {
  fill: rgba(255, 255, 255, 0.03);
  stroke: rgba(255, 255, 255, 0.1);
}
.dark-theme #dtp-svg-clock text {
  fill: #A9B4CC;
}
.dark-theme .dtp-select-minute[r="3"], .dark-theme .dtp-select-hour[r="3"] {
  fill: transparent;
}
.dark-theme .fc-other-month {
  background: rgba(249, 249, 249, 0.03);
}
.dark-theme .swal2-popup {
  background: #242526;
}
.dark-theme .swal2-popup .swal2-title {
  color: #e4e4e4;
}
.dark-theme .swal2-popup .swal2-html-container {
  color: #A9B4CC;
}
.dark-theme .swal2-popup a {
  color: #A9B4CC;
}
.dark-theme .dropzone .dz-message {
  color: #A9B4CC;
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .form-label {
  color: #A9B4CC;
}
.dark-theme .col-form-label {
  color: #A9B4CC;
}
.dark-theme .form-select,
.dark-theme .form-control {
  border-color: rgba(255, 255, 255, 0.15);
  color: #A9B4CC;
}
.dark-theme .form-select::-webkit-input-placeholder, .dark-theme .form-control::-webkit-input-placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .form-select::-moz-placeholder, .dark-theme .form-control::-moz-placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .form-select:-ms-input-placeholder, .dark-theme .form-control:-ms-input-placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .form-select::-ms-input-placeholder, .dark-theme .form-control::-ms-input-placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .form-select::placeholder,
.dark-theme .form-control::placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .form-floating label {
  color: #A9B4CC;
}
.dark-theme .form-floating .form-control::-webkit-input-placeholder {
  opacity: 0;
}
.dark-theme .form-floating .form-control::-moz-placeholder {
  opacity: 0;
}
.dark-theme .form-floating .form-control:-ms-input-placeholder {
  opacity: 0;
}
.dark-theme .form-floating .form-control::-ms-input-placeholder {
  opacity: 0;
}
.dark-theme .form-floating .form-control::placeholder {
  opacity: 0;
}
.dark-theme .table {
  --bs-table-striped-bg: rgba(255, 255, 255, 0.02);
  --bs-table-hover-bg: rgba(255, 255, 255, 0.03);
  color: #A9B4CC;
}
.dark-theme .table thead {
  background: rgba(255, 255, 255, 0.05);
}
.dark-theme .table.table-hover tbody tr:hover {
  background: rgba(0, 0, 0, 0.03);
}
.dark-theme .table.table-hover tbody tr:hover th, .dark-theme .table.table-hover tbody tr:hover td {
  color: #e4e4e4;
}
.dark-theme .table tr:hover td {
  color: #A9B4CC;
}
.dark-theme .table.table-striped-columns > :not(caption) > tr > :nth-child(2n),
.dark-theme .table.table-striped > tbody > tr:nth-of-type(odd) > * {
  color: #A9B4CC;
}
.dark-theme .table-responsive::-webkit-scrollbar-track {
  background: #3A3B3C;
}
.dark-theme .table-responsive::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}
.dark-theme .table-responsive::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}
.dark-theme .progress, .dark-theme .progress-stacked {
  --bs-progress-bg: #3A3B3C;
}
.dark-theme .form-check label {
  color: #A9B4CC;
}
.dark-theme .modal .modal-header .modal-title {
  color: #e4e4e4;
}
.dark-theme .modal .modal-content {
  background: #242526;
}
.dark-theme .badge.bg-primary-subtle {
  background-color: #3A3B3C !important;
  color: #e4e4e4;
}
.dark-theme .text-muted {
  color: #A9B4CC !important;
  opacity: 0.8;
  font-size: 13px;
}
.dark-theme .input-group-text {
  color: #A9B4CC;
}
.dark-theme .list-group-item {
  color: #A9B4CC;
}
.dark-theme .scrollable:hover .os-scrollbar-vertical .os-scrollbar-handle {
  background: rgba(255, 255, 255, 0.1) !important;
}
.dark-theme .scrollable:hover .os-scrollbar-vertical .os-scrollbar-handle:hover {
  background: rgba(255, 255, 255, 0.15) !important;
}
.dark-theme .panel-bg {
  background: #242526;
}
.dark-theme .panel {
  background: #242526;
}
.dark-theme .panel .panel-header {
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .panel .panel-header h5 {
  color: #e4e4e4;
}
.dark-theme .panel .panel-header form {
  border-color: rgba(255, 255, 255, 0.1);
}
.dark-theme .panel .panel-header form:focus-within {
  border-color: rgba(255, 255, 255, 0.2);
}
.dark-theme .progress-box p {
  color: #A9B4CC;
}
.dark-theme .btn-flush {
  color: #A9B4CC;
}
.dark-theme .btn-flush:hover, .dark-theme .btn-flush:focus {
  color: #fff;
}
.dark-theme .date-box {
  background: rgba(255, 255, 255, 0.05);
}
.dark-theme .digi-dropdown .dropdown-menu li .dropdown-item {
  color: #A9B4CC;
}
.dark-theme .digi-dropdown .dropdown-menu li .dropdown-item:hover {
  color: #fff;
}
.dark-theme .input-group-with-icon .input-icon {
  color: #A9B4CC;
}
.dark-theme .input-group-with-icon input {
  color: #A9B4CC;
}
.dark-theme .input-group-with-icon input::-webkit-input-placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .input-group-with-icon input::-moz-placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .input-group-with-icon input:-ms-input-placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .input-group-with-icon input::-ms-input-placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}
.dark-theme .input-group-with-icon input::placeholder {
  color: #A9B4CC;
  opacity: 0.6;
}

/* ===== Global Style For Light Theme ===== */
.light-theme .form-control[type=file]::-webkit-file-upload-button {
  color: #464646;
  background-color: rgba(0, 0, 0, 0.1);
}
.light-theme .form-control[type=file]::file-selector-button {
  color: #464646;
  background-color: rgba(0, 0, 0, 0.1);
}
.light-theme .form-control[type=file]:hover::-webkit-file-upload-button {
  background-color: #f1f4fb !important;
}
.light-theme .form-control[type=file]:hover::file-selector-button {
  background-color: #f1f4fb !important;
}
.light-theme input[type=radio],
.light-theme input[type=checkbox] {
  background-color: rgba(0, 0, 0, 0.07);
}
.light-theme .daterangepicker {
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.light-theme .dataTables_scrollBody::-webkit-scrollbar-track {
  background: #f5f5f5;
}
.light-theme .dataTables_scrollBody::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
}
.light-theme .dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
.light-theme .dataTables_wrapper .dataTables_length label {
  color: #464646;
}
.light-theme .dataTables_wrapper .dataTables_filter input {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .dataTables_wrapper .dataTables_info {
  color: #464646;
}
.light-theme table.dataTable thead .sorting {
  background-image: url(../vendor/images/sort_both_black.png);
}
.light-theme table.dataTable thead .sorting_asc {
  background-image: url(../vendor/images/sort_asc_black.png) !important;
}
.light-theme table.dataTable thead .sorting_desc {
  background-image: url(../vendor/images/sort_desc_black.png) !important;
}
.light-theme .ui-datepicker {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.1) !important;
}
.light-theme .ui-datepicker .ui-datepicker-header {
  background: rgba(0, 0, 0, 0.05);
  color: #313131;
}
.light-theme .ui-datepicker th {
  color: #313131;
}
.light-theme .ui-datepicker td {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .ui-datepicker .ui-datepicker-prev span::after,
.light-theme .ui-datepicker .ui-datepicker-next span::after {
  border-color: #313131;
}
.light-theme .ui-datepicker td a.ui-state-default:not(.ui-state-highlight) {
  background: #fff;
  color: #595959;
}
.light-theme .ui-datepicker td a.ui-priority-secondary {
  background: #fff;
  opacity: 0.5;
}
.light-theme .ui-datepicker td a.ui-state-active {
  background: rgba(0, 0, 0, 0.1) !important;
}
.light-theme .select2-dropdown {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.1) !important;
}
.light-theme .select2-results__options {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}
.light-theme .select2-results__options::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
}
.light-theme .select2-container--default .select2-results__option[aria-selected=true] {
  background: #ebebeb;
  color: #464646;
}
.light-theme .select2-container--default .select2-results__option[aria-selected=true]:hover {
  color: #464646;
}
.light-theme .select2-container--default .select2-selection--single .select2-selection__placeholder {
  color: #A9B4CC;
}
.light-theme .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #464646;
}
.light-theme .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background: rgba(0, 0, 0, 0.1);
  color: #464646;
  font-weight: 400;
}
.light-theme .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: #000;
}
.light-theme .select2-container .select2-search--inline .select2-search__field {
  color: #464646 !important;
}
.light-theme .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: rgba(0, 0, 0, 0.2);
  color: #464646 !important;
}
.light-theme .dtp > .dtp-content {
  background: #f5f5f5;
}
.light-theme .dtp div.dtp-date, .light-theme .dtp div.dtp-time {
  background: #fff;
}
.light-theme .dtp .p10 > a {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .dtp div.dtp-date, .light-theme .dtp div.dtp-time {
  color: #595959;
}
.light-theme .dtp div.dtp-actual-year, .light-theme .dtp div.dtp-actual-month {
  color: #797979;
}
.light-theme .dtp div.dtp-actual-num {
  font-weight: 500;
}
.light-theme .dtp div.dtp-picker-month, .light-theme .dtp div.dtp-actual-time {
  color: #464646;
}
.light-theme .year-picker-item {
  color: #797979;
}
.light-theme .dtp table.dtp-picker-days {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .dtp table.dtp-picker-days tr > th {
  background: rgba(0, 0, 0, 0.03);
  border-color: rgba(0, 0, 0, 0.1);
  color: #464646;
}
.light-theme .dtp table.dtp-picker-days tr > td span, .light-theme .dtp table.dtp-picker-days tr > td a {
  color: #595959;
}
.light-theme .dtp table.dtp-picker-days tr > td span.dtp-select-day {
  color: #A9B4CC !important;
  opacity: 0.8;
}
.light-theme .dtp table.dtp-picker-days tr > td > a.selected {
  color: #fff;
}
.light-theme #dtp-svg-clock circle:first-child {
  fill: rgba(0, 0, 0, 0.03);
  stroke: rgba(0, 0, 0, 0.05);
}
.light-theme #dtp-svg-clock text {
  fill: #464646;
}
.light-theme .fc-toolbar h2 {
  color: #000;
}
.light-theme .fc-unthemed th {
  background: rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .fc-unthemed td {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .fc-other-month {
  background: #F9F9F9;
}
.light-theme .fc-unthemed td.fc-today {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .swal2-popup {
  background: #fff;
}
.light-theme .swal2-popup .swal2-title {
  color: #464646;
}
.light-theme .swal2-popup .swal2-html-container {
  color: #595959;
}
.light-theme .swal2-popup a {
  color: #797979;
}
.light-theme .dropzone .dz-message {
  color: #464646;
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .form-label {
  color: #595959;
}
.light-theme .col-form-label {
  color: #595959;
}
.light-theme .form-select,
.light-theme .form-control {
  border-color: rgba(0, 0, 0, 0.15);
  color: #464646;
}
.light-theme .form-select::-webkit-input-placeholder, .light-theme .form-control::-webkit-input-placeholder {
  color: #A9B4CC;
}
.light-theme .form-select::-moz-placeholder, .light-theme .form-control::-moz-placeholder {
  color: #A9B4CC;
}
.light-theme .form-select:-ms-input-placeholder, .light-theme .form-control:-ms-input-placeholder {
  color: #A9B4CC;
}
.light-theme .form-select::-ms-input-placeholder, .light-theme .form-control::-ms-input-placeholder {
  color: #A9B4CC;
}
.light-theme .form-select::placeholder,
.light-theme .form-control::placeholder {
  color: #A9B4CC;
}
.light-theme .form-floating label {
  color: #464646;
}
.light-theme .form-floating .form-control::-webkit-input-placeholder {
  opacity: 0;
}
.light-theme .form-floating .form-control::-moz-placeholder {
  opacity: 0;
}
.light-theme .form-floating .form-control:-ms-input-placeholder {
  opacity: 0;
}
.light-theme .form-floating .form-control::-ms-input-placeholder {
  opacity: 0;
}
.light-theme .form-floating .form-control::placeholder {
  opacity: 0;
}
.light-theme .btn.show {
  color: #fff;
}
.light-theme .btn-outline-primary:active, .light-theme .btn-outline-primary.active {
  color: #fff;
}
.light-theme .btn-outline-primary:hover, .light-theme .btn-outline-primary:focus, .light-theme .btn-outline-primary:focus-visible {
  color: #fff;
}
.light-theme .btn-outline-secondary:not(:hover, :focus-visible, :active) {
  color: #595959;
}
.light-theme .table {
  --bs-table-striped-bg: rgba(0, 0, 0, 0.02);
  --bs-table-hover-bg: rgba(0, 0, 0, 0.03);
  color: #595959;
}
.light-theme .table th {
  color: #464646;
}
.light-theme .table thead {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .table.table-hover tbody tr:hover {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .table.table-hover tbody tr:hover th, .light-theme .table.table-hover tbody tr:hover td {
  color: #313131;
}
.light-theme .table tr:hover td {
  color: #595959;
}
.light-theme .table.table-striped-columns > :not(caption) > tr > :nth-child(2n),
.light-theme .table.table-striped > tbody > tr:nth-of-type(odd) > * {
  color: #464646;
}
.light-theme .table-responsive::-webkit-scrollbar-track {
  background: #f5f5f5;
}
.light-theme .table-responsive::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
}
.light-theme .table-responsive::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
.light-theme .progress, .light-theme .progress-stacked {
  --bs-progress-bg: #dbdbdb;
}
.light-theme .form-check label {
  color: #595959;
}
.light-theme .form-check.form-switch .form-check-input {
  --bs-form-switch-bg: url(../../images/circle-3.html);
}
.light-theme .form-check.form-switch .form-check-input:checked {
  --bs-form-switch-bg: url(../../images/circle-2.html);
}
.light-theme .form-check:not(.form-switch) .form-check-input:not(:focus) {
  border-color: rgba(0, 0, 0, 0.3);
}
.light-theme .form-check:not(.form-switch) .form-check-input:checked {
  background-image: url(../images/check-mark-black.png);
  background-color: #ebebeb;
}
.light-theme .form-check:not(.form-switch) .form-check-input[type=radio]:checked {
  background-image: url(../images/circle-3.png);
}
.light-theme .modal .modal-header {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .modal .modal-header .modal-title {
  color: #595959;
  font-weight: 600;
}
.light-theme .modal .modal-content {
  background: #fff;
}
.light-theme .badge.bg-primary-subtle {
  color: #000;
}
.light-theme .text-muted {
  color: #595959 !important;
}
.light-theme .card {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .card .card-header {
  background: rgba(0, 0, 0, 0.03);
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .input-group-text {
  color: #595959;
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .list-group-item {
  color: #464646;
}
.light-theme .scrollable:hover .os-scrollbar-vertical .os-scrollbar-handle {
  background: rgba(0, 0, 0, 0.15) !important;
}
.light-theme .scrollable:hover .os-scrollbar-vertical .os-scrollbar-handle:hover {
  background: rgba(0, 0, 0, 0.25) !important;
}
.light-theme .panel-bg {
  background: #fff;
}
.light-theme .panel {
  background: #fff;
}
.light-theme .panel .panel-header {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .panel .panel-header h5 {
  color: #464646;
}
.light-theme .panel .panel-header form {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .panel .panel-header form:focus-within {
  border-color: rgba(0, 0, 0, 0.2);
}
.light-theme .progress-box p {
  color: #595959;
}
.light-theme .btn-flush {
  color: #797979;
}
.light-theme .btn-flush:hover, .light-theme .btn-flush:focus {
  color: #000;
}
.light-theme .date-box {
  background: rgba(0, 0, 0, 0.05);
}
.light-theme .digi-dropdown .dropdown-menu li.dropdown-title {
  font-weight: 500;
  color: #464646;
}
.light-theme .digi-dropdown .dropdown-menu li .dropdown-item {
  color: #464646;
}
.light-theme .digi-dropdown .dropdown-menu li .dropdown-item:hover {
  color: #fff;
}
.light-theme .input-group-with-icon {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .input-group-with-icon .input-icon {
  color: #464646;
}
.light-theme .input-group-with-icon input {
  color: #464646;
}
.light-theme .input-group-with-icon input::-webkit-input-placeholder {
  color: #A9B4CC;
  opacity: 1;
}
.light-theme .input-group-with-icon input::-moz-placeholder {
  color: #A9B4CC;
  opacity: 1;
}
.light-theme .input-group-with-icon input:-ms-input-placeholder {
  color: #A9B4CC;
  opacity: 1;
}
.light-theme .input-group-with-icon input::-ms-input-placeholder {
  color: #A9B4CC;
  opacity: 1;
}
.light-theme .input-group-with-icon input::placeholder {
  color: #A9B4CC;
  opacity: 1;
}
.light-theme .utility-box {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .divider-dash {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme pre .attr-value {
  color: #464646;
}
.light-theme pre .punctuation {
  color: #595959;
  opacity: 0.7;
}

.check-mark .tick {
  -webkit-transform-origin: center;
          transform-origin: center;
  -webkit-animation: grow 0.8s ease-in-out forwards;
          animation: grow 0.8s ease-in-out forwards;
}
.check-mark .circle {
  -webkit-transform-origin: center;
          transform-origin: center;
  stroke-dasharray: 1000;
  stroke-dashoffset: 0;
  -webkit-animation: dash 1s linear;
          animation: dash 1s linear;
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(17, 33, 67, 0.6);
  -webkit-backdrop-filter: blur(3px);
          backdrop-filter: blur(3px);
  border-bottom: 1px solid rgba(223, 223, 223, 0.15);
  height: 70px;
  z-index: 1052;
}

.main-logo {
  width: 270px;
  height: 70px;
  overflow: hidden;
  position: relative;
  border-right: 1px dashed rgba(223, 223, 223, 0.15);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.main-logo .logo-big {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.main-logo .logo-small {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  -webkit-transform: translateY(-50%) translateX(-50%);
          transform: translateY(-50%) translateX(-50%);
  -webkit-transition: 0.3s;
  transition: 0.3s;
  opacity: 0;
}

.header.expanded .main-logo {
  width: 70px;
}

.header.expanded .main-logo .logo-big {
  display: none;
}

.header.expanded .main-logo .logo-small {
  opacity: 1;
}

.nav-close-btn button {
  color: #e9f1ff;
  background: transparent;
  padding: 0;
  font-size: 30px;
  border: 0;
}

.nav-close-btn button:hover,
.nav-close-btn button:focus {
  color: #f1f4fb;
}

.view-website {
  font-size: 14px;
  color: #e4e4e4;
  text-decoration: underline;
}

.header-right-btns {
  gap: 20px;
  padding-right: 20px;
}

.header-right-btns .header-form {
  background: #071739;
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.header-right-btns .header-form:focus-within input {
  width: 250px;
  padding: 0 15px;
}

.header-right-btns .header-form input {
  background: transparent;
  width: 0;
  height: 30px;
  font-size: 14px;
  padding: 0;
  color: #595959;
  border: 0;
  outline: none;
  color: #e4e4e4;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.header-right-btns .header-form button {
  padding: 0;
  width: 30px;
  background: transparent;
  color: #e4e4e4;
  font-size: 14px;
  border: 0;
}

.header-right-btns .lang-select {
  color: #fff;
}
.header-right-btns .lang-select select {
  background: transparent;
  color: #fff;
  border: 0;
  outline: none;
  cursor: pointer;
}
.header-right-btns .lang-select select option {
  color: #000;
}

.header-right-btns .header-btn-box {
  position: relative;
}

.header-right-btns .header-btn-box .badge {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform: translate(15%, -20%);
          transform: translate(15%, -20%);
  padding: 0 4px;
  text-align: center;
  min-width: 17px;
  height: 17px;
  line-height: 17px;
  font-size: 12px;
  font-weight: 300;
}

.header-right-btns .header-btn-box:last-child .dropdown-menu {
  padding-top: 0;
}

.header-btn {
  height: 30px;
  line-height: 30px;
  background: transparent;
  border: 0;
  font-size: 25px;
  color: #e9f1ff;
}
.header-btn.theme-settings-btn i {
  -webkit-animation: spin 5s linear infinite;
          animation: spin 5s linear infinite;
}

.header-btn.show {
  position: relative;
}
.header-btn.show::after {
  content: "";
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%) rotate(45deg);
          transform: translateX(-50%) rotate(45deg);
  bottom: -11px;
  width: 10px;
  height: 10px;
  background: #112143;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  border-left: 1px solid rgba(255, 255, 255, 0.15);
  border-top-left-radius: 4px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  z-index: 1001;
}
.header-btn.show .dropdown-menu {
  z-index: 1;
}

.header-right-btns .header-btn-box .message-dropdown {
  width: 280px;
  -webkit-box-shadow: 0px 0px 25px -10px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 0px 25px -10px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  padding: 10px 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.header-right-btns .header-btn-box .message-dropdown a {
  padding: 10px;
}

.header-right-btns .header-btn-box .message-dropdown a .avatar {
  margin-right: 10px;
}

.header-right-btns .header-btn-box .message-dropdown a .msg-txt {
  width: calc(100% - 50px);
}

.header-right-btns .header-btn-box .message-dropdown a .msg-txt span {
  display: block;
  color: #c8d4f0;
}

.header-right-btns .header-btn-box .message-dropdown a .msg-txt span.name {
  font-size: 13px;
  line-height: 100%;
  font-weight: 600;
  margin-bottom: 4px;
}

.header-right-btns .header-btn-box .message-dropdown a .msg-txt span.msg-short {
  font-family: "Lato", sans-serif;
  font-size: 14px;
  line-height: 20px;
  color: #8b97b2;
  margin-bottom: 9px;
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
}

.header-right-btns .header-btn-box .message-dropdown a .msg-txt span.time {
  font-size: 12px;
  line-height: 100%;
  font-weight: 500;
  color: #8b97b2;
}

.header-right-btns .header-btn-box .message-dropdown a:hover,
.header-right-btns .header-btn-box .message-dropdown a:focus {
  background: rgba(255, 255, 255, 0.1);
}

#notificationDropdown i.animate {
  -webkit-animation: wobble 0.9s cubic-bezier(0.36, 0.07, 0.19, 0.97) both infinite;
          animation: wobble 0.9s cubic-bezier(0.36, 0.07, 0.19, 0.97) both infinite;
}

.header-right-btns .header-btn-box .notification-dropdown {
  width: 280px;
  -webkit-box-shadow: 0px 0px 25px -10px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 0px 25px -10px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  padding: 10px 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.header-right-btns .header-btn-box .notification-dropdown a {
  padding: 10px;
}

.header-right-btns .header-btn-box .notification-dropdown a .avatar {
  margin-right: 10px;
}

.header-right-btns .header-btn-box .notification-dropdown a .notification-txt {
  font-family: "Lato", sans-serif;
  width: calc(100% - 50px);
  font-size: 14px;
  line-height: 100%;
  color: #c8d4f0;
}
.header-right-btns .header-btn-box .notification-dropdown a .notification-txt .fw-bold {
  font-weight: 700 !important;
}

.header-right-btns .header-btn-box .notification-dropdown a .notification-txt .notification-icon {
  font-size: 12px;
  color: #c8d4f0;
}

.header-right-btns .header-btn-box .notification-dropdown a:hover,
.header-right-btns .header-btn-box .notification-dropdown a:focus {
  background: rgba(255, 255, 255, 0.1);
}

.header-right-btns .header-btn-box .notification-dropdown a:hover .msg-txt span.name,
.header-right-btns .header-btn-box .notification-dropdown a:hover .msg-txt span.msg-short,
.header-right-btns .header-btn-box .notification-dropdown a:focus .msg-txt span.name,
.header-right-btns .header-btn-box .notification-dropdown a:focus .msg-txt span.msg-short {
  color: #595959;
}

.header-right-btns .dropdown-menu {
  top: 36px !important;
  -webkit-transform: translate(0) !important;
          transform: translate(0) !important;
}

.header-right-btns .header-btn-box .show-all-btn {
  display: block;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #c8d4f0;
  border-radius: 3px;
  padding: 5px 10px !important;
  margin: 10px 10px 0;
}

.header-right-btns .header-btn-box .show-all-btn:hover {
  background: #f1f4fb;
}

.header-right-btns .profile-btn {
  width: 30px;
  height: 30px;
  padding: 0;
  border: 0;
  border-radius: 50%;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  position: relative;
}
.header-right-btns .profile-btn img {
  border-radius: 15px;
}

.header-right-btns .profile-btn.show::after {
  content: "";
  position: absolute;
  left: 50%;
  -webkit-transform: translateX(-50%) rotate(45deg);
          transform: translateX(-50%) rotate(45deg);
  bottom: -11px;
  width: 10px;
  height: 10px;
  background: #112143;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  border-left: 1px solid rgba(255, 255, 255, 0.15);
  border-top-left-radius: 4px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  z-index: 1001;
}
.header-right-btns .profile-btn.show .dropdown-menu {
  z-index: 1;
}

.dropdown-menu {
  min-width: 200px;
  -webkit-box-shadow: 0px 0px 25px -5px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 0px 25px -5px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  background: #112143;
  padding: 10px 0;
  overflow: hidden;
}

.dropdown-menu .dropdown-icon {
  display: inline-block;
  width: 18px;
}

.dropdown-menu .dropdown-txt {
  font-size: 16px;
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 10px;
  color: #c8d4f0;
}
.dropdown-menu .dropdown-txt p {
  font-weight: 500;
}

.dropdown-menu .dropdown-txt span {
  font-size: 14px;
  color: #8b97b2;
  margin-bottom: -1px;
}

.dropdown-menu .dropdown-item {
  font-size: 14px;
  color: #c8d4f0;
  padding: 8px 15px;
}

.dropdown-menu .dropdown-item:hover {
  color: #fff;
}

.dropdown-menu-sm {
  padding: 5px 0;
  min-width: 150px;
}
.dropdown-menu-sm .dropdown-item {
  padding: 8px 10px;
}

.right-sidebar-btn {
  position: fixed;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 1051;
}
.right-sidebar-btn button {
  width: 45px;
  height: 40px;
  line-height: 42px;
  border-radius: 5px 0 0 5px;
  padding: 0;
}
.right-sidebar-btn button i {
  line-height: 1;
}

.profile-right-sidebar {
  position: fixed;
  top: 0;
  right: -350px;
  width: 300px;
  height: 100%;
  background: #0a1735;
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  -webkit-transition: 0.3s;
  transition: 0.3s;
  z-index: 1052;
}
.profile-right-sidebar.active {
  right: 0;
}
.profile-right-sidebar .dropdown-txt {
  font-size: 16px;
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 10px;
  color: #c8d4f0;
}
.profile-right-sidebar .dropdown-txt p {
  font-weight: 500;
}
.profile-right-sidebar .dropdown-txt span {
  font-size: 14px;
  color: #8b97b2;
  margin-bottom: -1px;
}
.profile-right-sidebar .dropdown-divider {
  border-top-width: 1px;
  border-top-style: solid;
}
.profile-right-sidebar .dropdown-item {
  font-size: 14px;
  color: #c8d4f0;
  padding: 8px 15px;
}
.profile-right-sidebar .dropdown-item:hover {
  color: #fff;
}
.profile-right-sidebar .top-panel {
  height: calc(100% - 71px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.profile-right-sidebar .bottom-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 10px;
}
.profile-right-sidebar .bottom-panel .button-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.profile-right-sidebar .bottom-panel .button-group a {
  width: calc(33.**********% - 6.7px);
  background: #112143;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 7px;
  color: #e4e4e4;
}
.profile-right-sidebar .bottom-panel .button-group a i {
  display: block;
  font-size: 16px;
  line-height: 1;
  margin-bottom: 9px;
}
.profile-right-sidebar .bottom-panel .button-group a span {
  display: block;
  font-size: 12px;
  line-height: 1;
  margin-bottom: -2px;
  color: #c4c4c4;
}
.profile-right-sidebar .bottom-panel .button-group a:hover {
  border-color: rgba(255, 255, 255, 0.3);
}

.calculator-dropdown {
  padding: 0;
}

.dgb-calc-box {
  width: 200px;
  padding: 7px;
}

#dgbCalcResult {
  width: 100%;
  height: 40px;
  padding: 10px 7px;
  font-size: 18px;
  font-weight: 400;
  text-align: right;
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  outline: none;
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
  margin-bottom: 5px;
}

#dgbCalcResult::-webkit-input-placeholder {
  color: #fff;
}

#dgbCalcResult::-moz-placeholder {
  color: #fff;
}

#dgbCalcResult:-ms-input-placeholder {
  color: #fff;
}

#dgbCalcResult::-ms-input-placeholder {
  color: #fff;
}

#dgbCalcResult::placeholder {
  color: #fff;
}

.dgb-calc-box table {
  width: calc(100% + 10px);
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  border-collapse: separate;
  border-spacing: 5px;
  margin: -5px;
}

.dgb-calc-box td {
  width: 25%;
  text-align: center !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  font-weight: 400;
  font-size: 12px;
  padding: 5px 0 !important;
  background: rgba(255, 255, 255, 0.05);
  color: #fff;
  cursor: pointer;
  -webkit-transition: 0.2s;
  transition: 0.2s;
}

.dgb-calc-box td:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* ===== Header For Dark Theme ===== */
.dark-theme .header {
  background: rgba(36, 37, 38, 0.6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.dark-theme .nav-close-btn button {
  color: #ebebeb;
}
.dark-theme .header-right-btns .header-form {
  background: rgba(36, 37, 38, 0.2);
}
.dark-theme .header-right-btns .header-form input {
  color: #f5f5f5;
}
.dark-theme .header-right-btns .header-form button {
  color: #ebebeb;
}
.dark-theme .header-right-btns .lang-select {
  color: #ebebeb;
}
.dark-theme .header-right-btns .lang-select select {
  color: #ebebeb;
}
.dark-theme .header-btn {
  color: #f1f4fb;
}
.dark-theme .header-right-btns .profile-btn.show::after,
.dark-theme .header-btn.show::after {
  background: #242526;
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .dropdown-divider,
.dark-theme .dropdown-menu {
  background: #242526;
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .dropdown-divider .dropdown-item,
.dark-theme .dropdown-menu .dropdown-item {
  color: #F9F9F9;
}
.dark-theme .dropdown-divider .dropdown-item:hover, .dark-theme .dropdown-divider .dropdown-item:focus,
.dark-theme .dropdown-menu .dropdown-item:hover,
.dark-theme .dropdown-menu .dropdown-item:focus {
  color: #fff;
}
.dark-theme .header-right-btns .header-btn-box .notification-dropdown a:hover,
.dark-theme .header-right-btns .header-btn-box .notification-dropdown a:focus,
.dark-theme .header-right-btns .header-btn-box .message-dropdown a:hover,
.dark-theme .header-right-btns .header-btn-box .message-dropdown a:focus {
  background: rgba(255, 255, 255, 0.1);
}
.dark-theme .dropdown-menu .dropdown-txt {
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .dropdown-menu .form-check-label {
  color: #F7F7F7;
}
.dark-theme .dropdown-menu .dropdown-txt,
.dark-theme .header-right-btns .header-btn-box .notification-dropdown a .notification-txt,
.dark-theme .header-right-btns .header-btn-box .show-all-btn,
.dark-theme .header-right-btns .header-btn-box .message-dropdown a .msg-txt span {
  color: #ebebeb;
}
.dark-theme .dropdown-menu .dropdown-txt span {
  color: #F9F9F9;
}
.dark-theme .profile-right-sidebar {
  background: #242526;
}
.dark-theme .profile-right-sidebar .right-bar-close {
  background: #242526;
}
.dark-theme .profile-right-sidebar .right-bar-close::before {
  background: #242526;
  border-color: rgba(255, 255, 255, 0.2);
}
.dark-theme .profile-right-sidebar .form-check-label {
  color: #F7F7F7;
}
.dark-theme .profile-right-sidebar .dropdown-txt {
  color: #ebebeb;
}
.dark-theme .profile-right-sidebar .dropdown-txt span {
  color: #F9F9F9;
}
.dark-theme .profile-right-sidebar .dropdown-item {
  color: #F9F9F9;
}
.dark-theme .profile-right-sidebar .dropdown-item:hover {
  color: #fff;
}
.dark-theme .profile-right-sidebar .bottom-panel .button-group a {
  background: #3A3B3C;
}

/* ===== Header For Light Theme ===== */
.light-theme .header {
  background: rgba(255, 255, 255, 0.6);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.light-theme .main-logo {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .nav-close-btn button {
  color: #595959;
}
.light-theme .header-right-btns .header-form {
  background: #F9F9F9;
}
.light-theme .header-right-btns .header-form input {
  color: #313131;
}
.light-theme .header-right-btns .header-form button {
  color: #595959;
}
.light-theme .header-right-btns .lang-select {
  color: #595959;
}
.light-theme .header-right-btns .lang-select select {
  color: #595959;
}
.light-theme .header-btn {
  color: #464646;
}
.light-theme .header-right-btns .profile-btn.show::after,
.light-theme .header-btn.show::after {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .dropdown-divider,
.light-theme .dropdown-menu {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .dropdown-divider .dropdown-item,
.light-theme .dropdown-menu .dropdown-item {
  color: #797979;
}
.light-theme .dropdown-divider .dropdown-item:hover, .light-theme .dropdown-divider .dropdown-item:focus,
.light-theme .dropdown-menu .dropdown-item:hover,
.light-theme .dropdown-menu .dropdown-item:focus {
  color: #fff;
}
.light-theme .header-right-btns .header-btn-box .notification-dropdown a:hover,
.light-theme .header-right-btns .header-btn-box .notification-dropdown a:focus,
.light-theme .header-right-btns .header-btn-box .message-dropdown a:hover,
.light-theme .header-right-btns .header-btn-box .message-dropdown a:focus {
  background: rgba(0, 0, 0, 0.05);
}
.light-theme .dropdown-menu .dropdown-txt {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .dropdown-menu .dropdown-txt,
.light-theme .header-right-btns .header-btn-box .notification-dropdown a .notification-txt,
.light-theme .header-right-btns .header-btn-box .show-all-btn,
.light-theme .header-right-btns .header-btn-box .message-dropdown a .msg-txt span {
  color: #595959;
}
.light-theme .dropdown-menu .dropdown-txt span {
  color: #797979;
}
.light-theme .profile-right-sidebar {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .profile-right-sidebar .right-bar-close {
  background: #fff;
}
.light-theme .profile-right-sidebar .right-bar-close::before {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.2);
}
.light-theme .profile-right-sidebar .form-check-label {
  color: #797979;
}
.light-theme .profile-right-sidebar .dropdown-txt {
  color: #595959;
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .profile-right-sidebar .dropdown-txt span {
  color: #797979;
}
.light-theme .profile-right-sidebar .dropdown-item {
  color: #797979;
}
.light-theme .profile-right-sidebar .dropdown-item:hover {
  color: #464646;
}
.light-theme .profile-right-sidebar .top-panel {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .profile-right-sidebar .bottom-panel .button-group a {
  background: #f5f5f5;
  border-color: rgba(0, 0, 0, 0.07);
  color: #313131;
}
.light-theme .profile-right-sidebar .bottom-panel .button-group a span {
  color: #595959;
}
.light-theme .profile-right-sidebar .bottom-panel .button-group a:hover {
  border-color: rgba(0, 0, 0, 0.2);
}
.light-theme #dgbCalcResult {
  background-color: #f5f5f5;
  border-color: rgba(0, 0, 0, 0.1);
  color: #464646;
}
.light-theme #dgbCalcResult::-webkit-input-placeholder {
  color: #464646;
}
.light-theme #dgbCalcResult::-moz-placeholder {
  color: #464646;
}
.light-theme #dgbCalcResult:-ms-input-placeholder {
  color: #464646;
}
.light-theme #dgbCalcResult::-ms-input-placeholder {
  color: #464646;
}
.light-theme #dgbCalcResult::placeholder {
  color: #464646;
}
.light-theme .dgb-calc-box td {
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.05);
  color: #464646;
}
.light-theme .dgb-calc-box td[class|=bg], .light-theme .dgb-calc-box td[class|=dgb-calc] {
  color: #fff !important;
}
.light-theme .dgb-calc-box td:hover {
  background: rgba(0, 0, 0, 0.1);
}
.light-theme .right-sidebar-btn button {
  color: #fff;
}

.right-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 350px;
  height: 100%;
  background: #0a1735;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  z-index: 1052;
}
.right-sidebar.active {
  right: 0;
}
.right-sidebar .sidebar-title {
  height: 40px;
  padding: 0 20px;
  color: #fff;
}
.right-sidebar .sidebar-title h3 {
  font-family: "Lato", sans-serif;
  font-size: 16px;
  line-height: 40px;
  font-weight: 600;
}
.right-sidebar .sidebar-body {
  padding: 20px;
  height: calc(100% - 40px);
  border-color: rgba(255, 255, 255, 0.2);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}
.right-sidebar .right-sidebar-group {
  margin-bottom: 20px;
  -webkit-transition: none;
  transition: none;
}
.right-sidebar .right-sidebar-group:last-child {
  padding-bottom: 20px;
  margin-bottom: 0;
}
.right-sidebar .sidebar-subtitle {
  display: block;
  font-size: 14px;
  line-height: 100%;
  color: #F7F7F7;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: -1px;
  margin-bottom: 20px;
}
.right-sidebar .sidebar-subtitle span {
  font-size: 12px;
  text-align: center;
  width: 16px;
  height: 16px;
  line-height: 14px;
  border: 1px solid rgba(121, 121, 121, 0.25);
  border-radius: 50%;
  cursor: pointer;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.right-sidebar .sidebar-subtitle .collapsed {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.right-sidebar .settings-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.right-sidebar .settings-row .settings-col {
  width: calc(33.**********% - 6.7px);
}
.right-sidebar .settings-row-2 {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.right-sidebar .dashboard-icon {
  border: 1px solid rgba(121, 121, 121, 0.25) !important;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  overflow: hidden;
  cursor: pointer;
  position: relative;
}
.right-sidebar .dashboard-icon.light-theme-btn .part-txt {
  background: rgba(70, 70, 70, 0.75);
}
.right-sidebar .dashboard-icon::after {
  content: "\f00c";
  font-family: "Font Awesome 6 Pro";
  font-weight: 700;
  line-height: 15px;
  color: #fff;
  font-size: 10px;
  position: absolute;
  top: 0;
  right: 0;
  text-align: center;
  width: 15px;
  height: 15px;
  border-radius: 0 2px 0 3px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  opacity: 0;
}
.right-sidebar .dashboard-icon .part-txt {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(121, 121, 121, 0.25);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  text-align: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-family: "Lato", sans-serif;
  font-size: 12px;
  line-height: 16px;
  font-weight: 300;
  color: #e4e4e4;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.right-sidebar .dashboard-icon:hover .part-txt {
  opacity: 1;
}
.right-sidebar .dashboard-icon.active::after {
  opacity: 1;
}
.right-sidebar .bg-menu {
  border-color: rgba(121, 121, 121, 0.25) !important;
}
.right-sidebar .color-palette {
  width: calc(20% - 8px);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0;
  border-radius: 3px;
  overflow: hidden;
  outline: none;
  position: relative;
}
.right-sidebar .color-palette::after {
  content: "\f00c";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-family: "Font Awesome 6 Pro";
  font-weight: 300;
  color: #fff;
  background: rgba(70, 70, 70, 0.5);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  opacity: 0;
}
.right-sidebar .color-palette.active::after {
  opacity: 1;
}
.right-sidebar .color-palette span {
  display: inline-block;
  width: 25%;
  height: 25px;
}
.right-sidebar .color-palette-1 {
  border: 1px solid #0D99FF;
}
.right-sidebar .color-palette-1 span {
  background: #0D99FF;
}
.right-sidebar .color-palette-1 span:nth-child(2) {
  background: #112143;
}
.right-sidebar .color-palette-1 span:nth-child(3) {
  background: #071739;
}
.right-sidebar .color-palette-1 span:nth-child(4) {
  background: #0a1735;
}
.right-sidebar .color-palette-2 {
  border: 1px solid #F19C49;
}
.right-sidebar .color-palette-2 span {
  background: #F19C49;
}
.right-sidebar .color-palette-2 span:nth-child(2) {
  background: #c37837;
}
.right-sidebar .color-palette-2 span:nth-child(3) {
  background: #bd722b;
}
.right-sidebar .color-palette-2 span:nth-child(4) {
  background: #4d3118;
}
.right-sidebar .color-palette-3 {
  border: 1px solid #F6ABB6;
}
.right-sidebar .color-palette-3 span {
  background: #F6ABB6;
}
.right-sidebar .color-palette-3 span:nth-child(2) {
  background: #c5939b;
}
.right-sidebar .color-palette-3 span:nth-child(3) {
  background: #a87980;
}
.right-sidebar .color-palette-3 span:nth-child(4) {
  background: #4a3538;
}
.right-sidebar .color-palette-4 {
  border: 1px solid #2189a3;
}
.right-sidebar .color-palette-4 span {
  background: #2189a3;
}
.right-sidebar .color-palette-4 span:nth-child(2) {
  background: #1e748a;
}
.right-sidebar .color-palette-4 span:nth-child(3) {
  background: #1c6a7d;
}
.right-sidebar .color-palette-4 span:nth-child(4) {
  background: #14434f;
}
.right-sidebar .color-palette-5 {
  border: 1px solid #8528a6;
}
.right-sidebar .color-palette-5 span {
  background: #8528a6;
}
.right-sidebar .color-palette-5 span:nth-child(2) {
  background: #672180;
}
.right-sidebar .color-palette-5 span:nth-child(3) {
  background: #5B0D76;
}
.right-sidebar .color-palette-5 span:nth-child(4) {
  background: #3c144a;
}
.right-sidebar .color-palette-6 {
  border: 1px solid #F0C434;
}
.right-sidebar .color-palette-6 span {
  background: #F0C434;
}
.right-sidebar .color-palette-6 span:nth-child(2) {
  background: #c29e2b;
}
.right-sidebar .color-palette-6 span:nth-child(3) {
  background: #b59631;
}
.right-sidebar .color-palette-6 span:nth-child(4) {
  background: #594a17;
}
.right-sidebar .color-palette-7 {
  border: 1px solid #47b522;
}
.right-sidebar .color-palette-7 span {
  background: #47b522;
}
.right-sidebar .color-palette-7 span:nth-child(2) {
  background: #44a623;
}
.right-sidebar .color-palette-7 span:nth-child(3) {
  background: #388c1c;
}
.right-sidebar .color-palette-7 span:nth-child(4) {
  background: #255914;
}
.right-sidebar .color-palette-8 {
  border: 1px solid #FF1493;
}
.right-sidebar .color-palette-8 span {
  background: #FF1493;
}
.right-sidebar .color-palette-8 span:nth-child(2) {
  background: #cc1478;
}
.right-sidebar .color-palette-8 span:nth-child(3) {
  background: #9e115e;
}
.right-sidebar .color-palette-8 span:nth-child(4) {
  background: #5e0c39;
}
.right-sidebar .color-palette-9 {
  border: 1px solid #b0c391;
}
.right-sidebar .color-palette-9 span {
  background: #b0c391;
}
.right-sidebar .color-palette-9 span:nth-child(2) {
  background: #94a578;
}
.right-sidebar .color-palette-9 span:nth-child(3) {
  background: #76855d;
}
.right-sidebar .color-palette-9 span:nth-child(4) {
  background: #495437;
}
.right-sidebar .color-palette-10 {
  border: 1px solid #b2bd42;
}
.right-sidebar .color-palette-10 span {
  background: #b2bd42;
}
.right-sidebar .color-palette-10 span:nth-child(2) {
  background: #868f2f;
}
.right-sidebar .color-palette-10 span:nth-child(3) {
  background: #828a2d;
}
.right-sidebar .color-palette-10 span:nth-child(4) {
  background: #3c4015;
}
.right-sidebar .sidebar-bg-btn-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.right-sidebar .sidebar-bg-btn-box button {
  width: 50px;
  height: 90px;
  padding: 0;
  background-size: cover;
  border: 1px solid #797979;
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
  font-size: 20px;
  position: relative;
}
.right-sidebar .sidebar-bg-btn-box button#noBackground::after {
  display: none;
}
.right-sidebar .sidebar-bg-btn-box button::after {
  content: "\f00c";
  font-family: "Font Awesome 6 Pro";
  font-weight: 100;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #fff;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.right-sidebar .sidebar-bg-btn-box button.active::after {
  opacity: 1;
}
.right-sidebar .main-content-bg-btn-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.right-sidebar .main-content-bg-btn-box button {
  width: calc(33.**********% - 6.7px);
  height: 60px;
  padding: 0;
  background-size: cover;
  border: 1px solid #797979;
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
  font-size: 20px;
  position: relative;
}
.right-sidebar .main-content-bg-btn-box button#noBackground2::after {
  display: none;
}
.right-sidebar .main-content-bg-btn-box button::after {
  content: "\f00c";
  font-family: "Font Awesome 6 Pro";
  font-weight: 100;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  color: #fff;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.right-sidebar .main-content-bg-btn-box button.active::after {
  opacity: 1;
}
.right-sidebar .preloader-small {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  opacity: 0.5;
}
.right-sidebar .preloader-small .loader {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 5px;
}
.right-sidebar .preloader-small .loader span {
  width: 5px;
  height: 5px;
  background: #c8d4f0;
  border-radius: 50%;
}
.right-sidebar .preloader-small .loader span:nth-child(1) {
  -webkit-animation: loader 1s infinite alternate;
          animation: loader 1s infinite alternate;
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}
.right-sidebar .preloader-small .loader span:nth-child(2) {
  -webkit-animation: loader 1s infinite alternate;
          animation: loader 1s infinite alternate;
  -webkit-animation-delay: 0.5s;
          animation-delay: 0.5s;
}
.right-sidebar .preloader-small .loader span:nth-child(3) {
  -webkit-animation: loader 1s infinite alternate;
          animation: loader 1s infinite alternate;
  -webkit-animation-delay: 1s;
          animation-delay: 1s;
}

.right-bar-close {
  position: absolute;
  top: 50%;
  right: calc(100% - 2px);
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  padding: 5px 2px;
  border: 0;
  background: #0a1735;
  color: #dfdfdf;
  font-size: 30px;
  -webkit-perspective: 70px;
          perspective: 70px;
  z-index: 2;
}
.right-bar-close::before {
  content: "";
  position: absolute;
  top: -1px;
  right: -5px;
  width: 200%;
  height: calc(100% + 6px);
  background: #0a1735;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-left-width: 2px;
  border-right-width: 0;
  border-radius: 10px 0 0 10px;
  -webkit-transform: rotateY(-50deg) translateY(-2px);
          transform: rotateY(-50deg) translateY(-2px);
  z-index: -1;
}
.right-bar-close::after {
  content: "";
  position: absolute;
  top: 4px;
  right: -1px;
  width: 155%;
  height: calc(100% - 4px);
  border-radius: 5px;
  -webkit-transform: rotateY(-53deg) translateY(-2px);
          transform: rotateY(-53deg) translateY(-2px);
  z-index: -1;
}
.right-bar-close i {
  margin-right: 5px;
}

/* ===== Right Sidebar For Dark Theme ===== */
.dark-theme .right-sidebar {
  background: #242526;
}
.dark-theme .right-sidebar .right-bar-close {
  background: #242526;
}
.dark-theme .right-sidebar .right-bar-close::before {
  background: #242526;
  border-color: rgba(255, 255, 255, 0.2);
}
.dark-theme .right-sidebar .sidebar-body {
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .right-sidebar .sidebar-subtitle {
  color: #e4e4e4;
}
.dark-theme .right-sidebar .sidebar-subtitle span {
  border-color: rgba(255, 255, 255, 0.5);
}
.dark-theme .right-sidebar .main-content-bg-btn-box button,
.dark-theme .right-sidebar .sidebar-bg-btn-box button {
  border-color: #464646;
}

/* ===== Right Sidebar For Light Theme ===== */
.light-theme .right-sidebar {
  background: #fff;
}
.light-theme .right-sidebar .right-bar-close {
  background: #fff;
}
.light-theme .right-sidebar .right-bar-close::before {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.2);
}
.light-theme .right-sidebar .sidebar-body {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .right-sidebar .sidebar-subtitle {
  color: #000;
}
.light-theme .right-sidebar .sidebar-subtitle span {
  border-color: rgba(0, 0, 0, 0.5);
}
.light-theme .right-sidebar .dashboard-icon .part-txt {
  background: rgba(0, 0, 0, 0.5);
}
.light-theme .right-sidebar .main-content-bg-btn-box button,
.light-theme .right-sidebar .sidebar-bg-btn-box button {
  border-color: rgba(0, 0, 0, 0.15);
}

.main-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 270px;
  padding-top: 70px;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: #112143;
  z-index: 11;
}
.main-sidebar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 270px;
  height: 100%;
  background: rgba(17, 33, 67, 0.93);
  z-index: -1;
}
.main-sidebar.horizontal-menu {
  top: 70px;
  bottom: auto;
  width: 100%;
  padding: 0;
  -webkit-transition: 0s;
  transition: 0s;
  z-index: 111;
}
.main-sidebar.horizontal-menu::after {
  width: 100%;
  height: calc(100% + 1px);
  -webkit-transition: 0s;
  transition: 0s;
}
.main-sidebar.horizontal-menu .main-menu {
  padding: 0 30px;
}
.main-sidebar.horizontal-menu .sidebar-menu {
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 25px;
}
.main-sidebar.horizontal-menu .sidebar-item {
  font-weight: 400;
  position: relative;
  margin-bottom: 0;
  border: 0;
}
.main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link {
  color: #e9f1ff;
  padding-left: 15px;
}
.main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link.active {
  background: rgba(0, 0, 0, 0.2);
  font-weight: 400;
}
.main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link.active::after {
  background: #fff;
}
.main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link .nav-icon {
  display: none;
}
.main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link:hover {
  background: rgba(0, 0, 0, 0.2);
}
.main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item:hover .sidebar-dropdown-menu {
  opacity: 1;
  visibility: visible;
}
.main-sidebar.horizontal-menu .sidebar-link-group-title {
  background: transparent;
  padding: 14px 25px 14px 0;
}
.main-sidebar.horizontal-menu .sidebar-link-group-title::after {
  right: 0;
  -webkit-transform: translateY(-55%) rotate(180deg);
          transform: translateY(-55%) rotate(180deg);
  opacity: 1;
  font-weight: 300;
  font-size: 14px;
}
.main-sidebar.horizontal-menu .sidebar-link-group {
  position: absolute;
  top: 100%;
  left: 0;
  width: auto;
  min-width: 180px;
  background: #112143;
  border: 1px solid rgba(223, 223, 223, 0.15);
  border-radius: 3px;
  padding: 0;
}
.main-sidebar.horizontal-menu .sidebar-link-group.show {
  display: block !important;
  -webkit-animation: dropdown 0.2s linear;
          animation: dropdown 0.2s linear;
}
.main-sidebar.horizontal-menu .sidebar-dropdown-menu {
  background: #112143;
  border: 1px solid rgba(223, 223, 223, 0.15);
  border-radius: 3px;
  position: absolute;
  top: 0;
  left: 100%;
  min-width: 180px;
  padding-left: 0;
}
.main-sidebar.horizontal-menu .sidebar-dropdown-menu.show {
  display: block !important;
  -webkit-animation: dropdown 0.2s linear;
          animation: dropdown 0.2s linear;
}
.main-sidebar.horizontal-menu .sidebar-item .sidebar-link.show:after {
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.main-sidebar.horizontal-menu .help-center {
  display: none;
}
.main-sidebar.two-column-menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.main-sidebar.two-column-menu.open-sub {
  width: 270px;
}
.main-sidebar.two-column-menu .sidebar-menu .sidebar-link {
  border-bottom: 1px solid rgba(255, 255, 255, 0.04);
}
.main-sidebar.two-column-menu .sidebar-menu .sidebar-link.has-sub {
  position: relative;
  overflow: hidden;
}
.main-sidebar.two-column-menu .sidebar-menu .sidebar-link.has-sub::before {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  border: 4px solid transparent;
  border-bottom-color: #c8d4f0;
  border-right-color: #c8d4f0;
  opacity: 0.6;
  display: block;
}
.main-sidebar.sidebar-hover {
  width: 70px;
}
.main-sidebar.sidebar-hover::after {
  width: 100%;
}
.main-sidebar.sidebar-hover .sidebar-item {
  width: 50px;
  margin-bottom: 10px;
}
.main-sidebar.sidebar-hover .sidebar-link-group-title {
  display: none;
}
.main-sidebar.sidebar-hover .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active::after {
  left: 0;
}
.main-sidebar.sidebar-hover .sidebar-link-group {
  padding: 0;
}
.main-sidebar.sidebar-hover .sidebar-link-group .sidebar-link.has-sub.show {
  border-radius: 2px 2px 0 0;
}
.main-sidebar.sidebar-hover .main-menu {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  padding: 10px 0 10px 10px;
}
.main-sidebar.sidebar-hover .sidebar-sub-title {
  font-size: 0;
  padding: 0;
}
.main-sidebar.sidebar-hover .sidebar-item .sidebar-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0 10px;
  height: 40px;
  line-height: 40px;
}
.main-sidebar.sidebar-hover .sidebar-item .sidebar-link .nav-icon {
  text-align: center;
}
.main-sidebar.sidebar-hover .sidebar-item .sidebar-link.has-sub.show .nav-icon {
  color: #e9f1ff;
}
.main-sidebar.sidebar-hover .sidebar-item .sidebar-link .sidebar-txt {
  visibility: hidden;
  opacity: 0;
  font-size: 0;
}
.main-sidebar.sidebar-hover .sidebar-item .sidebar-link .nav-icon {
  font-size: 16px;
  color: #c8d4f0;
  margin-right: 0;
}
.main-sidebar.sidebar-hover .sidebar-item .sidebar-link.has-sub:after {
  display: none;
}
.main-sidebar.sidebar-hover .sidebar-menu .help-center {
  display: none;
}
.main-sidebar.flush-menu.collapsed .sidebar-item {
  width: 70px;
  margin: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0;
}
.main-sidebar.flush-menu.collapsed .sidebar-item:nth-last-child(2) {
  border-bottom: 0;
}
.main-sidebar.flush-menu.collapsed .sidebar-item .sidebar-dropdown-item .sidebar-link {
  padding: 0 10px;
}
.main-sidebar.flush-menu.sidebar-hover .main-menu {
  margin: auto;
  padding: 0;
}
.main-sidebar.flush-menu.sidebar-hover .sidebar-item {
  width: 70px;
  margin: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0;
}
.main-sidebar.flush-menu.sidebar-hover .sidebar-item .sidebar-dropdown-item .sidebar-link {
  padding: 0 10px;
}
.main-sidebar.flush-menu .main-menu {
  padding-left: 0;
  padding-right: 0;
}
.main-sidebar.flush-menu .sidebar-menu {
  padding-right: 0;
}
.main-sidebar.flush-menu .sidebar-item {
  border: 0;
}
.main-sidebar.flush-menu .sidebar-link-group-title {
  padding: 0 15px;
  height: 11px;
  line-height: 12px;
  background: transparent;
  margin-bottom: 10px;
}
.main-sidebar.flush-menu .sidebar-link-group {
  padding: 0;
}
.main-sidebar.flush-menu .sidebar-item .sidebar-dropdown-item .sidebar-link {
  padding: 10px 20px;
}
.main-sidebar.flush-menu .sidebar-item .sidebar-dropdown-item .sidebar-dropdown-menu .sidebar-link {
  padding-left: 40px;
}
.main-sidebar.flush-menu .help-center {
  margin: 0 15px;
}

.main-sidebar.collapsed {
  position: relative;
  padding-top: 0;
  width: 70px;
}
.main-sidebar.collapsed::after {
  width: 100%;
}
.main-sidebar.collapsed.two-column-menu {
  width: 270px;
  position: fixed;
  padding-top: 70px;
}
.main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu {
  width: 100%;
  position: static;
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
  border-color: rgba(223, 223, 223, 0.15);
  border-left: 1px solid rgba(223, 223, 223, 0.15);
  border-radius: 0;
  padding: 0 0 0 15px;
}
.main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu[id] {
  width: 200px;
  padding: 10px 0;
}
.main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link {
  display: block;
  font-size: 14px;
  padding: 10px;
  color: #c8d4f0;
}
.main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link.active {
  background: #071739;
  color: #F9F9F9;
}
.main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link.has-sub {
  position: relative;
}
.main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link.has-sub::after {
  content: "\f105";
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 20px;
  font-size: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  opacity: 0.8;
}
.main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link.has-sub.show::after {
  -webkit-transform: translateY(-50%) rotate(90deg);
          transform: translateY(-50%) rotate(90deg);
}
.main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link:hover {
  color: #F9F9F9;
}
.main-sidebar.collapsed.sub-menu-collapsed {
  width: 70px;
}
.main-sidebar.collapsed.sub-menu-collapsed .sidebar-dropdown-menu {
  display: none !important;
}
.main-sidebar.collapsed.flush-menu .os-content {
  gap: 0;
}
.main-sidebar.collapsed.flush-menu .sidebar-menu {
  gap: 0;
}
.main-sidebar.collapsed .sidebar-item .sidebar-dropdown-item .sidebar-dropdown-menu .sidebar-link {
  padding-left: 10px;
}
.main-sidebar.collapsed .sidebar-item .sidebar-dropdown-item .sidebar-dropdown-menu .sidebar-link:first-child {
  border-radius: 3px 3px 0 0;
}
.main-sidebar.collapsed .sidebar-item .sidebar-dropdown-item .sidebar-dropdown-menu .sidebar-link:last-child {
  border-radius: 0 0 3px 3px;
}
.main-sidebar.collapsed .os-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 10px;
}
.main-sidebar.collapsed .os-content::before, .main-sidebar.collapsed .os-content::after {
  display: none;
}
.main-sidebar.collapsed .sidebar-menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 10px;
}

.main-menu {
  padding: 10px 0 10px 10px;
  height: 100%;
}

.sidebar-menu {
  height: 100%;
  padding-right: 10px;
}
.sidebar-menu .os-scrollbar-vertical {
  padding: 0;
  width: 6px;
}
.sidebar-menu .os-scrollbar-vertical .os-scrollbar-handle {
  background: transparent !important;
}
.sidebar-menu:hover .os-scrollbar-vertical .os-scrollbar-handle {
  background: rgba(255, 255, 255, 0.2) !important;
}

.sidebar-sub-title {
  font-size: 12px;
  line-height: 100%;
  font-weight: 700;
  text-transform: uppercase;
  color: #fff;
  padding: 0 15px;
  padding-top: 9px;
  padding-bottom: 8px;
}

.sidebar-sub-title.mt--1 {
  margin-top: -1px;
  padding-top: 0;
}

.sidebar-item {
  font-size: 14px;
  font-weight: 500;
  border-radius: 3px;
  margin-bottom: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.collapsed .sidebar-item {
  width: 50px;
  margin-bottom: 0;
}

.sidebar-item:last-child {
  margin-bottom: 0;
}

.sidebar-item .sidebar-link {
  display: block;
  padding: 10px 15px;
  background: #071739;
  position: relative;
  color: #A9B4CC;
}

.sidebar-link-group-title {
  display: block;
  padding: 10px 15px;
  border-radius: 2px 2px 0 0;
  position: relative;
  color: #e4e4e4;
}
.sidebar-link-group-title.show {
  border-radius: 2px;
}
.sidebar-link-group-title.show::after {
  -webkit-transform: translateY(-50%) rotate(180deg);
          transform: translateY(-50%) rotate(180deg);
}
.sidebar-link-group-title:after {
  content: "\f106";
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 15px;
  font-size: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  opacity: 0.8;
}
.sidebar-link-group-title:hover {
  color: #e4e4e4;
}

.collapsed .sidebar-link-group-title {
  display: none;
}

.sidebar-item .sidebar-link.has-sub {
  position: relative;
}

.sidebar-item .sidebar-link.has-sub:after {
  content: "\f105";
  font-family: "Font Awesome 6 Pro";
  font-weight: 900;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  right: 20px;
  font-size: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  opacity: 0.8;
}

.sidebar-item .sidebar-link.show {
  color: #e4e4e4 !important;
}
.sidebar-item .sidebar-link.show .nav-icon {
  color: #e4e4e4;
}

.sidebar-item .sidebar-link.show:after {
  -webkit-transform: translateY(-50%) rotate(90deg);
          transform: translateY(-50%) rotate(90deg);
}

.sidebar-item .sidebar-link .nav-icon {
  display: inline-block;
  width: 18px;
  margin-right: 5px;
  color: #8b97b2;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.sidebar-item .sidebar-link:hover,
.sidebar-item .sidebar-link:focus {
  background: #071739;
}

.sidebar-item.open .sidebar-link {
  color: #fff;
}

.sidebar-dropdown-item {
  position: relative;
}

.sidebar-item.open .sidebar-dropdown-item .sidebar-link {
  background: transparent;
  color: #A9B4CC;
}

.sidebar-item .sidebar-dropdown-item .sidebar-dropdown-menu .sidebar-link {
  padding-left: 35px;
}

.sidebar-item.open .sidebar-dropdown-item .sidebar-link.active {
  font-weight: 600;
  background: #071739;
}
.sidebar-item.open .sidebar-dropdown-item .sidebar-link.active::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0px;
  width: 10px;
  height: 1px;
  background: #8b97b2;
}

.collapsed .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active::after {
  left: 0;
}

.sidebar-item .sidebar-dropdown-item .sidebar-link {
  background: transparent;
  color: #8b97b2;
  padding: 10px 15px;
}

.sidebar-item .sidebar-dropdown-item .sidebar-link:hover,
.sidebar-item .sidebar-dropdown-item .sidebar-link.active {
  color: #e4e4e4;
}

.sidebar-item .sidebar-dropdown-item .sidebar-link:hover .nav-icon,
.sidebar-item .sidebar-dropdown-item .sidebar-link.active .nav-icon {
  color: #e4e4e4;
}

.sidebar-link-group > *:last-child .sidebar-link {
  border-radius: 0 0 2px 2px;
}

.collapsed .sidebar-link-group {
  padding: 0;
}
.collapsed .sidebar-link-group .sidebar-link.has-sub.show {
  border-radius: 2px;
}

.sidebar-dropdown-menu {
  display: none;
}
.sidebar-dropdown-menu .sidebar-dropdown-menu {
  padding-left: 20px;
}

.collapsed .main-menu {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  padding: 10px 0 10px 10px;
}

.collapsed .sidebar-sub-title {
  font-size: 0;
  padding: 0;
}

.collapsed .sidebar-item .sidebar-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 0 10px;
  height: 40px;
  line-height: 40px;
}
.collapsed .sidebar-item .sidebar-link .nav-icon {
  text-align: center;
}

.collapsed .sidebar-item .sidebar-link.has-sub.show .nav-icon {
  color: #e9f1ff;
}

.collapsed .sidebar-item .sidebar-link .sidebar-txt {
  visibility: hidden;
  opacity: 0;
  font-size: 0;
}

.collapsed .sidebar-item .sidebar-link .nav-icon {
  font-size: 16px;
  color: #c8d4f0;
  margin-right: 0;
}

.collapsed .sidebar-dropdown-menu {
  position: absolute;
  padding: 0;
  top: 0;
  left: 100%;
  width: 200px;
  background: #112143;
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
          box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
.collapsed .sidebar-dropdown-menu .sidebar-link {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.collapsed .sidebar-link-group:last-child .sidebar-dropdown-item:last-child .sidebar-dropdown-menu {
  top: auto;
  bottom: 0;
}

.collapsed .sidebar-item .sidebar-link.has-sub:after {
  display: none;
}

.collapsed .sidebar-item .sidebar-dropdown-item .sidebar-dropdown-item .sidebar-link.has-sub:after {
  display: inline-block;
}

.sidebar-menu .help-center {
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  text-align: center;
  padding: 40px 25px;
  color: #fff;
}
.sidebar-menu .help-center h3 {
  font-family: "Lato", sans-serif;
  font-size: 24px;
  line-height: 100%;
  font-weight: 600;
  margin-bottom: 22px;
}
.sidebar-menu .help-center p {
  margin-bottom: 23px;
}

.collapsed .sidebar-menu .help-center {
  display: none;
}

/* ===== Left Sidebar For Dark Theme ===== */
.dark-theme .main-sidebar {
  background-color: #242526;
}
.dark-theme .main-sidebar::after {
  background: rgba(36, 37, 38, 0.9);
}
.dark-theme .main-sidebar.horizontal-menu .sidebar-dropdown-menu,
.dark-theme .main-sidebar.horizontal-menu .sidebar-link-group {
  background: #242526;
  border-color: rgba(255, 255, 255, 0.1);
}
.dark-theme .main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link {
  color: #F9F9F9;
}
.dark-theme .main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link.active {
  background: rgba(255, 255, 255, 0.03);
}
.dark-theme .main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link.active::after {
  background: rgba(255, 255, 255, 0.3);
}
.dark-theme .main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link:hover {
  background: rgba(255, 255, 255, 0.03);
}
.dark-theme .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.1);
}
.dark-theme .main-sidebar.collapsed.two-column-menu .sidebar-item .sidebar-link.has-sub.show .nav-icon {
  color: #fff;
}
.dark-theme .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link {
  color: #ebebeb;
}
.dark-theme .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link.active {
  background: rgba(255, 255, 255, 0.03);
  color: #fff;
}
.dark-theme .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link:hover {
  color: #fff;
}
.dark-theme .main-sidebar.flush-menu .sidebar-link-group-title {
  color: #fff;
}
.dark-theme .main-sidebar.sidebar-hover .sidebar-item .sidebar-link .nav-icon {
  color: #F9F9F9;
}
.dark-theme .sidebar-item .sidebar-dropdown-item .sidebar-link:hover .nav-icon, .dark-theme .sidebar-item .sidebar-dropdown-item .sidebar-link.active .nav-icon {
  color: #f1f4fb;
}
.dark-theme .sidebar-item .sidebar-link .nav-icon,
.dark-theme .sidebar-item .sidebar-dropdown-item .sidebar-link {
  color: #A9B4CC;
}
.dark-theme .sidebar-item .sidebar-link .nav-icon:hover,
.dark-theme .sidebar-item .sidebar-dropdown-item .sidebar-link:hover {
  color: #ebebeb;
}
.dark-theme .sidebar-item .sidebar-link.show {
  color: #ebebeb !important;
}
.dark-theme .sidebar-item .sidebar-link.show .nav-icon {
  color: #ebebeb;
}
.dark-theme .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active {
  background: #313131;
  color: #ebebeb;
}
.dark-theme .sidebar-link-group-title {
  color: #f5f5f5;
}
.dark-theme .collapsed .sidebar-dropdown-menu {
  background: #242526;
}
.dark-theme .collapsed .sidebar-item .sidebar-link.has-sub.show {
  background: #313131;
}
.dark-theme .collapsed .sidebar-item .sidebar-link.has-sub.show .nav-icon {
  color: #ebebeb;
}

/* ===== Left Sidebar For Light Theme ===== */
.light-theme .main-sidebar {
  background-color: #fff;
}
.light-theme .main-sidebar::after {
  background: rgba(255, 255, 255, 0.85);
}
.light-theme .main-sidebar.horizontal-menu .sidebar-dropdown-menu,
.light-theme .main-sidebar.horizontal-menu .sidebar-link-group {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link {
  color: #797979;
}
.light-theme .main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link.active {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link.active::after {
  background: rgba(0, 0, 0, 0.3);
}
.light-theme .main-sidebar.horizontal-menu .sidebar-item .sidebar-dropdown-item .sidebar-link:hover {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-sidebar.two-column-menu .sidebar-menu .sidebar-link {
  border-color: rgba(0, 0, 0, 0.04);
}
.light-theme .main-sidebar.two-column-menu .sidebar-menu .sidebar-link.has-sub::before {
  border-bottom-color: rgba(0, 0, 0, 0.5);
  border-right-color: rgba(0, 0, 0, 0.5);
}
.light-theme .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu {
  background: transparent;
  border-color: rgba(0, 0, 0, 0.05);
}
.light-theme .main-sidebar.collapsed.two-column-menu .sidebar-item .sidebar-link.has-sub.show .nav-icon {
  color: #fff;
}
.light-theme .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link {
  color: #595959;
}
.light-theme .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link.active {
  background: rgba(0, 0, 0, 0.03);
  color: #000;
}
.light-theme .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link:hover {
  color: #000;
}
.light-theme .main-sidebar.flush-menu .sidebar-link-group-title {
  color: #000;
}
.light-theme .main-sidebar.sidebar-hover .sidebar-item .sidebar-link .nav-icon {
  color: #797979;
}
.light-theme .sidebar-item .sidebar-dropdown-item .sidebar-link:hover .nav-icon, .light-theme .sidebar-item .sidebar-dropdown-item .sidebar-link.active .nav-icon {
  color: #464646;
}
.light-theme .sidebar-item .sidebar-link .nav-icon,
.light-theme .sidebar-item .sidebar-dropdown-item .sidebar-link {
  color: #797979;
}
.light-theme .sidebar-item .sidebar-link .nav-icon:hover,
.light-theme .sidebar-item .sidebar-dropdown-item .sidebar-link:hover {
  color: #595959;
}
.light-theme .sidebar-item .sidebar-link.show {
  color: #595959 !important;
}
.light-theme .sidebar-item .sidebar-link.show .nav-icon {
  color: #595959;
}
.light-theme .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active {
  background: #f5f5f5;
  color: #595959;
}
.light-theme .sidebar-link-group-title {
  color: #fff;
}
.light-theme .collapsed .sidebar-dropdown-menu {
  background: #fff;
}
.light-theme .collapsed .sidebar-item .sidebar-link.has-sub.show {
  background: #f5f5f5;
}
.light-theme .collapsed .sidebar-item .sidebar-link.has-sub.show .nav-icon {
  color: #595959;
}

.body-padding {
  padding-left: 270px;
}
.body-padding.expanded {
  padding-left: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.body-padding.expanded.has-fixed-sidebar {
  padding-left: 70px;
}
.body-padding.expanded .main-content {
  width: 100%;
}
.body-padding.hover-menu {
  padding-left: 70px;
}

.main-content {
  width: 100%;
  position: relative;
  padding: 25px 25px 70px;
  z-index: 2;
}
.main-content::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(../images/bg.jpg) center center no-repeat;
  background-size: cover;
  opacity: 0.12;
  z-index: -1;
}
.main-content.login-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(../images/auth-bg.png) center center no-repeat;
  background-size: cover;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.main-content.login-panel::after {
  background: transparent;
}
.main-content.login-panel.login-panel-2 .panel-title {
  font-family: "Lato", sans-serif;
  font-size: 40px;
  line-height: 100%;
  font-weight: 400;
  color: #fff;
  padding-bottom: 12px;
  margin-top: -5px;
  margin-bottom: 40px;
  position: relative;
}
.main-content.login-panel.login-panel-2 .panel-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 50px;
  height: 2px;
}
.main-content.login-panel.login-panel-3 {
  background: url(../images/login-bg.jpg) center center no-repeat;
  background-size: cover;
  z-index: 2;
}
.main-content.login-panel.login-panel-3::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: -webkit-gradient(linear, left top, right top, from(rgba(17, 33, 67, 0.25)), to(rgba(0, 0, 0, 0.2)));
  background: linear-gradient(to right, rgba(17, 33, 67, 0.25), rgba(0, 0, 0, 0.2));
  opacity: 1;
  z-index: -1;
}
.main-content.login-panel.login-panel-3 .container {
  background: url(../images/login.png) center left no-repeat;
  background-size: 500px;
}
.main-content.login-panel .footer {
  border-top: 1px solid var(--bs-border-color);
}
.main-content.welcome-panel {
  background-image: url(../images/welcome-bg.png);
}
.main-content.welcome-panel .static-body {
  background: rgba(49, 49, 49, 0.8);
}
.main-content.email-verify-panel {
  background-image: url(../images/verify-email-bg.png);
}
.main-content.email-verify-panel .static-body {
  background: rgba(49, 49, 49, 0.8);
}
.main-content.two-factor-panel {
  background-image: url(../images/two-factor-bg.png);
  color: #f7faff;
}
.main-content.two-factor-panel .static-body {
  width: 100%;
  max-width: 600px;
  margin-left: auto;
  background: rgba(49, 49, 49, 0.8);
}
.main-content.multi-step-signup-panel {
  background: transparent;
}
.main-content .dashboard-breadcrumb {
  background: #112143;
  padding: 15px 20px;
  border-radius: 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.main-content .dashboard-breadcrumb h2 {
  font-family: "Lato", sans-serif;
  font-size: 24px;
  line-height: 19px;
  font-weight: 600;
  color: #e4e4e4;
  margin-bottom: 0;
}
.main-content .dashboard-filter {
  width: 250px;
  background: #112143;
  border-radius: 3px;
}
.main-content .dashboard-filter .form-control {
  font-family: "Lato", sans-serif;
  width: auto;
  display: inline;
  border-radius: 3px 0 0 3px;
  color: #e4e4e4;
  cursor: pointer;
}
.main-content .dashboard-filter .input-group-text {
  color: #e4e4e4;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  border-right-width: 1px;
  cursor: pointer;
}
.main-content .dashboard-top-box {
  padding: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  color: #e4e4e4 !important;
}
.main-content .dashboard-top-box.dashboard-top-box-2 {
  padding: 0;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.main-content .dashboard-top-box.dashboard-top-box-2 .left {
  padding: 20px;
}
.main-content .dashboard-top-box.dashboard-top-box-2 .left p {
  margin-top: -2px;
}
.main-content .dashboard-top-box.dashboard-top-box-2 .left p.text-muted {
  margin-bottom: -3px;
}
.main-content .dashboard-top-box.dashboard-top-box-2 .left h3 {
  padding-top: 4px;
  margin-bottom: 21px;
}
.main-content .dashboard-top-box.dashboard-top-box-2 .right {
  height: 100%;
  padding: 20px;
  border-left: 1px dashed rgba(255, 255, 255, 0.15);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .dashboard-top-box.dashboard-top-box-2 .right .part-icon {
  width: 60px;
  height: 60px;
  line-height: 60px;
}
.main-content .dashboard-top-box.dashboard-top-box-2 .right .part-icon span {
  font-size: 25px;
}
.main-content .dashboard-top-box .left h3 {
  font-size: 16px;
  line-height: 100%;
  font-weight: 600;
  margin-bottom: 11px;
}
.main-content .dashboard-top-box .left p {
  font-size: 14px;
  line-height: 100%;
  margin-bottom: 34px;
}
.main-content .dashboard-top-box .left a {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  display: block;
  font-size: 12px;
  color: #e4e4e4;
  text-decoration: underline;
  margin-bottom: -3px;
}
.main-content .dashboard-top-box .right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}
.main-content .dashboard-top-box .right span {
  font-size: 12px;
  font-weight: 600;
  margin-top: -4px;
}
.main-content .dashboard-top-box .right .part-icon {
  text-align: center;
  width: 35px;
  height: 35px;
  line-height: 35px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  background: #071739;
}
.main-content .dashboard-top-box .right .part-icon span {
  font-size: 16px;
  margin: 0;
}
.main-content .dashboard-top-box:hover .right .part-icon {
  color: #e4e4e4;
}
.main-content .visitor-table {
  color: #e4e4e4;
  margin-bottom: -2px;
}
.main-content .visitor-table th {
  font-weight: 400;
}
.main-content .visitor-table th:last-child {
  text-align: right;
}
.main-content .visitor-table tr:last-child td {
  padding-bottom: 0;
}
.main-content .visitor-table td:last-child {
  text-align: right;
}
.main-content .visitor-table td span {
  display: inline-block;
  text-align: center;
  width: 20px;
  height: 20px;
  line-height: 22px;
  font-size: 14px;
  color: #fff;
  border-radius: 3px;
  margin-right: 10px;
}
.main-content .recent-project-table {
  color: #c8d4f0;
  font-size: 14px;
}
.main-content .recent-project-table thead {
  background: rgba(255, 255, 255, 0.05);
}
.main-content .recent-project-table th {
  font-weight: 400;
}
.main-content .recent-project-table th:last-child {
  text-align: right;
}
.main-content .recent-project-table td {
  color: #c8d4f0 !important;
  vertical-align: middle;
}
.main-content .recent-project-table td span:last-child {
  font-size: 12px;
}
.main-content .recent-project-table tr:last-child td {
  border-bottom: 0;
}
.main-content .activity-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  color: #F0F0F0;
}
.main-content .activity-box .part-txt span {
  display: block;
  font-size: 14px;
  line-height: 1.2;
  margin-top: -2px;
}
.main-content .activity-box .part-txt span:first-child {
  margin-bottom: 13px;
}
.main-content .table-activity {
  margin-bottom: 0;
  border: 1px dashed rgba(255, 255, 255, 0.1);
}
.main-content .table-activity tr:last-child td {
  border-bottom: 0;
}
.main-content .table-activity tr:hover .date-box {
  color: #fff;
}
.main-content .table-activity td {
  border-style: dashed;
  vertical-align: middle;
  padding: 15px;
}
.main-content .hr-recent-activity {
  color: #c8d4f0;
  padding-left: 8px;
}
.main-content .hr-recent-activity li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  padding-left: 22px;
  padding-bottom: 30px;
  position: relative;
}
.main-content .hr-recent-activity li::before {
  content: "";
  position: absolute;
  top: 13px;
  bottom: 2px;
  left: 0;
  border-left: 2px dotted;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  opacity: 0.3;
}
.main-content .hr-recent-activity li::after {
  content: "";
  position: absolute;
  top: -2px;
  left: 0;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  opacity: 0.3;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .hr-recent-activity li:last-child {
  padding-bottom: 0;
}
.main-content .hr-recent-activity li:last-child::before {
  display: none;
}
.main-content .hr-recent-activity li .left {
  width: calc(100% - 110px);
}
.main-content .hr-recent-activity li .activity-name {
  display: block;
  font-size: 14px;
  line-height: 100%;
  color: #fff;
  margin-top: -2px;
  margin-bottom: 9px;
}
.main-content .hr-recent-activity li .activity-short {
  display: block;
  font-size: 13px;
  line-height: 1.3;
  margin-bottom: -4px;
}
.main-content .hr-recent-activity li .activity-time {
  text-align: right;
  width: 100px;
  display: block;
  font-size: 14px;
  line-height: 100%;
  margin-top: -2px;
}
.main-content .hr-recent-activity li:hover::after {
  opacity: 1;
}
.main-content .pending-task-table {
  margin-bottom: 0;
}
.main-content .pending-task-table tr:last-child td {
  border-bottom: 0;
}
.main-content .pending-task-table td {
  vertical-align: middle;
  padding: 10px 20px;
}
.main-content .pending-task-table .task-box span {
  display: block;
  line-height: 100%;
}
.main-content .pending-task-table .task-box span:first-child {
  margin-bottom: 12px;
}
.main-content .invoice-table {
  margin-bottom: 0;
}
.main-content .invoice-table th {
  padding: 10px 20px;
  font-weight: 400;
}
.main-content .invoice-table th:last-child {
  text-align: right;
}
.main-content .invoice-table td {
  padding: 15.7px 20px;
  vertical-align: middle;
}
.main-content .invoice-table thead {
  background: rgba(255, 255, 255, 0.05);
}
.main-content .invoice-table tbody tr:last-child td {
  border-bottom: 0;
}
.main-content .task-table {
  margin-bottom: 0;
}
.main-content .task-table td {
  padding: 10px 20px;
  vertical-align: middle;
}
.main-content .task-table td:last-child {
  text-align: right;
}
.main-content .task-table .form-check label {
  padding: 0;
  color: #c8d4f0;
}
.main-content .notice-board-table {
  margin-bottom: 0;
}
.main-content .notice-board-table thead {
  background: rgba(255, 255, 255, 0.05);
}
.main-content .notice-board-table th {
  font-weight: 400;
  padding: 5px 15px;
}
.main-content .notice-board-table th:last-child {
  text-align: right;
}
.main-content .notice-board-table td {
  padding: 15px 15px;
}
.main-content .notice-board-table tr:last-child td {
  border-bottom: 0;
}
.main-content .notice-board-table .btn-box button {
  font-size: 14px;
}
.main-content .hr-notice-board li {
  margin-bottom: 20px;
}
.main-content .hr-notice-board li:last-child {
  margin-bottom: 0;
}
.main-content .hr-notice-board li:hover .date-box {
  color: #fff;
}
.main-content .deadline-table {
  margin-bottom: 0;
}
.main-content .deadline-table thead {
  background: rgba(255, 255, 255, 0.05);
}
.main-content .deadline-table th {
  font-weight: 400;
  padding: 5px 15px;
}
.main-content .deadline-table th:last-child {
  text-align: center;
}
.main-content .deadline-table td {
  padding: 13.7px 15px;
}
.main-content .deadline-table td:last-child {
  text-align: center;
}
.main-content .deadline-table tr:last-child td {
  border-bottom: 0;
}
.main-content .deadline-table .btn-box button {
  font-size: 14px;
}
.main-content .new-customer-table {
  color: #e4e4e4;
  margin-bottom: 0;
  vertical-align: middle;
}
.main-content .new-customer-table tr td {
  padding: 12.5px 0;
}
.main-content .new-customer-table tr:first-child td {
  padding-top: 0;
}
.main-content .new-customer-table tr:last-child td {
  padding-bottom: 0;
}
.main-content .new-customer-table .new-customer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
}
.main-content .new-customer-table .new-customer .part-img {
  width: 60px;
  height: 60px;
  border-radius: 3px;
  overflow: hidden;
}
.main-content .new-customer-table .new-customer .part-txt {
  width: calc(100% - 75px);
}
.main-content .new-customer-table .new-customer .part-txt p {
  font-family: "Lato", sans-serif;
  font-size: 18px;
  line-height: 100%;
  font-weight: 500;
  margin-top: -3px;
  margin-bottom: 12px;
}
.main-content .new-customer-table .new-customer .part-txt span {
  display: block;
  font-size: 14px;
  line-height: 100%;
  color: #A9B4CC;
  margin-bottom: -2px;
}
.main-content .recent-order-table {
  max-width: 100%;
  width: 100% !important;
  font-family: "Lato", sans-serif;
  border-bottom: 0 !important;
  color: #c8d4f0;
  font-size: 14px;
  vertical-align: middle;
}
.main-content .recent-order-table thead {
  color: #f7faff;
  background: #071739;
}
.main-content .recent-order-table thead th {
  font-weight: 400;
  padding: 13px 30px 13px 10px;
  background-position-x: calc(100% - 10px);
  background-size: 13px;
}
.main-content .recent-order-table tr {
  background: transparent;
}
.main-content .recent-order-table tr.selected {
  background: #0a1735;
}
.main-content .recent-order-table tr th {
  border-bottom: 1px dashed rgba(223, 223, 223, 0.15);
}
.main-content .recent-order-table tr td {
  text-align: center;
  padding: 14px 15px 15px 10px;
  border-color: rgba(223, 223, 223, 0.15);
  border-bottom: 1px dashed rgba(223, 223, 223, 0.15);
}
.main-content .recent-order-table tr td:first-child {
  text-align: left;
  border-left: 1px dashed rgba(223, 223, 223, 0.15);
}
.main-content .recent-order-table tr td:last-child {
  text-align: right;
  border-right: 1px dashed rgba(223, 223, 223, 0.15);
}
.main-content .recent-order-table .badge {
  width: 100%;
}
.main-content .recent-order-table .btn-box {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  margin-left: auto;
  gap: 15px;
}
.main-content .recent-order-table .btn-box button {
  padding: 0;
  background: transparent;
  border: 0;
  color: #c8d4f0;
  font-size: 16px;
  line-height: 100%;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .recent-order-table .btn-box button:hover, .main-content .recent-order-table .btn-box button:focus {
  color: #fff;
}
.main-content .table-bottom-control {
  border: 1px dashed rgba(223, 223, 223, 0.15);
  border-top: 0;
  color: #c8d4f0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 20px;
  font-size: 14px;
}
.main-content .table-bottom-control .dataTables_paginate {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}
.main-content .table-bottom-control .dataTables_paginate span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}
.main-content .table-bottom-control .btn {
  padding: 0;
  width: 20px;
  height: 20px;
  line-height: 20px;
  border: 0;
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
  font-size: 12px;
  background: #071739;
}
.main-content .table-bottom-control .previous,
.main-content .table-bottom-control .next {
  width: auto;
  background-color: transparent;
  padding: 0;
  border: 0;
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
  font-size: 14px;
}
.main-content .table-bottom-control .previous:active, .main-content .table-bottom-control .previous:focus,
.main-content .table-bottom-control .next:active,
.main-content .table-bottom-control .next:focus {
  background-color: transparent !important;
}
.main-content .upcoming-interview li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}
.main-content .upcoming-interview li .avatar {
  width: 65px;
  height: 65px;
}
.main-content .upcoming-interview li .part-txt span {
  display: block;
  line-height: 100%;
  color: #dfe9ff;
}
.main-content .upcoming-interview li .part-txt span.applicant-name {
  font-size: 15px;
  margin-top: -1px;
  margin-bottom: 10px;
}
.main-content .upcoming-interview li .part-txt span.applicant-role {
  margin-bottom: -2px;
}
.main-content .upcoming-interview li:last-child {
  margin-bottom: 0;
}
.main-content .login-body {
  width: 500px;
  background: rgba(49, 49, 49, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.main-content .login-body.login-body-2 {
  padding: 50px;
}
.main-content .login-body.login-body-2 .top {
  height: 90px;
  background: #fff;
  border-radius: 3px;
  padding: 0 20px;
  margin-bottom: 30px;
}
.main-content .login-body.login-body-2 .bottom {
  padding: 0;
}
.main-content .login-body .top {
  height: 100px;
  padding: 0 30px;
  border-bottom: 1px solid rgba(223, 223, 223, 0.15);
}
.main-content .login-body .top a {
  color: #F9F9F9;
}
.main-content .login-body .bottom {
  padding: 50px;
}
.main-content .login-body .bottom .panel-title {
  text-align: center;
  font-size: 24px;
  line-height: 100%;
  font-weight: 400;
  color: #F9F9F9;
  margin-top: -2px;
  margin-bottom: 26px;
}
.main-content .login-body .bottom .input-group-text {
  width: 50px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background: #fff;
}
.main-content .login-body .bottom .form-control {
  background: #fff;
  border-width: 0;
  border-left: 1px solid rgba(49, 49, 49, 0.15);
  height: 50px;
  color: #595959;
  font-size: 14px;
}
.main-content .login-body .bottom .form-control:not(:focus) {
  border-color: rgba(49, 49, 49, 0.15);
}
.main-content .login-body .bottom .other-option {
  text-align: center;
  padding-top: 20px;
}
.main-content .login-body .bottom .other-option p {
  font-size: 16px;
  line-height: 100%;
  color: #c8d4f0;
  margin-top: -2px;
  margin-bottom: 17px;
}
.main-content .login-body .bottom .other-option p.mb-0 {
  margin-bottom: -3px !important;
}
.main-content .login-body .bottom .other-option .social-box a {
  text-align: center;
  width: 45px;
  height: 45px;
  line-height: 45px;
  background: #F7F7F7;
  border-radius: 3px;
  font-size: 22px;
}
.main-content .login-body .bottom .other-option .social-box a:hover {
  color: #F7F7F7;
}
.main-content .login-body .bottom .input-group {
  position: relative;
}
.main-content .login-body .bottom .input-group .password-show {
  position: absolute;
  top: 50%;
  right: 10px;
  color: #595959;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  cursor: pointer;
  z-index: 5;
}
.main-content .login-body .login-btn {
  position: relative;
  z-index: 2;
}
.main-content .login-body .login-btn::after {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  width: 0;
  height: calc(100% + 2px);
  background: #2d893a;
  border-radius: 3px;
  z-index: -1;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}
.main-content .login-body .login-btn:focus::after {
  width: calc(100% + 2px);
}
.main-content .login-body a.fs-14 {
  line-height: 100%;
}
.main-content .static-body {
  width: 600px;
  background: rgba(49, 49, 49, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.main-content .static-body .panel-body {
  padding: 50px;
}
.main-content .static-body .logo {
  margin-bottom: 50px;
}
.main-content .static-body .part-txt {
  margin-bottom: 50px;
}
.main-content .static-body .part-txt h2 {
  font-size: 30px;
  line-height: 1;
  font-weight: 500;
  color: #fff;
  margin-top: -3px;
  margin-bottom: 21px;
}
.main-content .static-body .part-txt p {
  font-size: 14px;
  line-height: 1.5;
  color: #F9F9F9;
  margin-bottom: 24px;
}
.main-content .static-body .part-img {
  padding: 0 20px;
}
.main-content .verification-area {
  color: #e9f1ff;
}
.main-content .verification-area #otp_target {
  margin-bottom: 20px;
}
.main-content .verification-area .fake-inputs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}
.main-content .verification-area .inputs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
}
.main-content .verification-area .inputs input {
  text-align: center;
  width: 16.6666666667%;
  aspect-ratio: 1/0.8;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 5px;
  outline: none;
  color: #fff;
  font-size: 30px;
}
.main-content .verification-area .realInput {
  opacity: 0;
}
.main-content .verification-area .otp-fake-input .otp-content {
  color: #fff;
  font-weight: 400;
}
.main-content .steps-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 450px;
  height: calc(100% - 45px);
  padding: 100px 60px;
  z-index: 2;
}
.main-content .steps-sidebar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 100px;
  width: 100%;
  height: 100%;
  background: inherit;
  z-index: -1;
}
.main-content .steps-sidebar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100% + 100px);
  height: 100%;
  background: url(../images/vector.png) bottom center no-repeat;
  background-size: contain;
  opacity: 0.17;
  z-index: -1;
}
.main-content .steps-sidebar .sidebar-logo {
  -webkit-filter: brightness(10);
          filter: brightness(10);
  height: 25px;
  margin-bottom: 50px;
}
.main-content .steps-sidebar .sidebar-content {
  height: calc(100% - 75px);
}
.main-content .steps-sidebar .sidebar-content .step-list {
  padding-right: 10px;
  margin-right: -10px;
}
.main-content .steps-sidebar .sidebar-content .step-list:hover .os-scrollbar-vertical .os-scrollbar-handle {
  background: rgba(0, 0, 0, 0.2) !important;
}
.main-content .steps-sidebar .sidebar-content .step-list li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  margin-bottom: 40px;
  position: relative;
}
.main-content .steps-sidebar .sidebar-content .step-list li::after {
  content: "";
  position: absolute;
  top: 100%;
  right: 24px;
  height: 40px;
  border: 1px dashed rgba(255, 255, 255, 0.4);
}
.main-content .steps-sidebar .sidebar-content .step-list li:last-child {
  margin-bottom: 0;
}
.main-content .steps-sidebar .sidebar-content .step-list li:last-child::after {
  display: none;
}
.main-content .steps-sidebar .sidebar-content .step-list li.completed .step-number::after {
  content: "\f00c";
  color: #2d893a;
  background: #d1e7dd;
  border-color: rgba(45, 137, 58, 0.5);
  border-style: solid;
  opacity: 1;
}
.main-content .steps-sidebar .sidebar-content .step-list li.active .step-number {
  color: #2d893a;
  z-index: 2;
}
.main-content .steps-sidebar .sidebar-content .step-list li.active .step-number::after {
  background: #d1e7dd;
  border-color: rgba(45, 137, 58, 0.5);
  opacity: 1;
  z-index: -1;
}
.main-content .steps-sidebar .sidebar-content .step-list span {
  display: block;
}
.main-content .steps-sidebar .sidebar-content .step-list .step-txt {
  width: calc(100% - 60px);
}
.main-content .steps-sidebar .sidebar-content .step-list .step-txt .step-name {
  font-size: 18px;
  line-height: 1;
  font-weight: 500;
  color: #fff;
  margin-bottom: 10px;
}
.main-content .steps-sidebar .sidebar-content .step-list .step-txt .step-info {
  font-size: 14px;
  line-height: 1;
  font-weight: 400;
  color: #fff;
  opacity: 0.9;
}
.main-content .steps-sidebar .sidebar-content .step-list .step-number {
  text-align: center;
  width: 50px;
  height: 50px;
  line-height: 50px;
  background: #fff;
  border-radius: 5px;
  color: #464646;
  font-size: 20px;
  position: relative;
}
.main-content .steps-sidebar .sidebar-content .step-list .step-number::after {
  content: "";
  font-family: "Font Awesome 6 Pro";
  font-weight: 700;
  line-height: 38px;
  color: transparent;
  position: absolute;
  top: 3px;
  right: 3px;
  bottom: 3px;
  left: 3px;
  border: 2px dotted;
  border-color: inherit;
  border-radius: 5px;
  opacity: 0.5;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .all-steps {
  position: fixed;
  top: 50%;
  right: 100px;
  -webkit-transform: translateY(calc(-50% - 22.5px));
          transform: translateY(calc(-50% - 22.5px));
  width: calc(100% - 550px);
  height: calc(100% - 250px);
  background: rgba(24, 25, 26, 0.8);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  overflow: auto;
  z-index: 2;
}
.main-content .all-steps .single-step {
  width: 100%;
  display: none;
  height: 100%;
}
.main-content .all-steps .single-step.show {
  display: block;
  -webkit-animation: slideIn 0.4s ease-in-out;
          animation: slideIn 0.4s ease-in-out;
}
.main-content .all-steps .step-content-wrap {
  max-width: 500px;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  height: -webkit-max-content;
  height: -moz-max-content;
  height: max-content;
  margin: auto;
}
.main-content .all-steps .step-content-wrap .step-content {
  width: 100%;
  margin-bottom: 40px;
}
.main-content .all-steps .step-heading {
  text-align: center;
  margin-bottom: 33px;
}
.main-content .all-steps .account-types {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 20px;
}
.main-content .all-steps .account-types.account-plan .form-check label {
  padding: 10px;
}
.main-content .all-steps .account-types.account-plan .form-check label .title {
  font-size: 16px;
  margin-bottom: 8px;
}
.main-content .all-steps .account-types.account-plan .form-check label .dscr {
  font-size: 12px;
}
.main-content .all-steps .account-types .form-check {
  width: 100%;
  max-width: 350px;
}
.main-content .all-steps .account-types .form-check label {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid transparent;
  border-radius: 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .all-steps .account-types .form-check label .part-icon {
  width: 30px;
  display: block;
  font-size: 30px;
  color: #fff;
}
.main-content .all-steps .account-types .form-check label .part-txt {
  width: calc(100% - 45px);
}
.main-content .all-steps .account-types .form-check label .title {
  display: block;
  font-size: 20px;
  line-height: 1;
  color: #fff;
  margin-bottom: 10px;
}
.main-content .all-steps .account-types .form-check label .dscr {
  display: block;
  font-size: 14px;
  color: #bdbdbd;
  line-height: 1.4;
}
.main-content .all-steps .account-types .form-check input {
  display: none;
}
.main-content .all-steps .account-types .form-check input:checked + label {
  background: rgba(255, 255, 255, 0.1);
  border-color: inherit;
}
.main-content .all-steps .account-info-title {
  display: block;
  width: 100%;
  font-size: 14px;
  line-height: 1;
  color: #fff;
  margin-bottom: 13px;
}
.main-content .all-steps .team-size {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  margin-bottom: 30px;
}
.main-content .all-steps .team-size .form-check {
  width: 25%;
}
.main-content .all-steps .team-size .form-check:first-child label {
  border-radius: 5px 0 0 5px;
}
.main-content .all-steps .team-size .form-check:last-child label {
  border-radius: 0 5px 5px 0;
}
.main-content .all-steps .team-size .form-check label {
  text-align: center;
  width: 100%;
  height: 60px;
  line-height: 60px;
  font-size: 20px;
  padding: 0;
  background: rgba(255, 255, 255, 0.05);
  color: #fff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .all-steps .team-size .form-check input {
  display: none;
}
.main-content .all-steps .team-size .form-check input:checked + label {
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
}
.main-content .all-steps .team-title {
  margin-bottom: 30px;
}
.main-content .all-steps .form-control {
  color: #fff;
}
.main-content .all-steps .form-control::-webkit-input-placeholder {
  color: #bdbdbd;
  opacity: 0.5;
}
.main-content .all-steps .form-control::-moz-placeholder {
  color: #bdbdbd;
  opacity: 0.5;
}
.main-content .all-steps .form-control:-ms-input-placeholder {
  color: #bdbdbd;
  opacity: 0.5;
}
.main-content .all-steps .form-control::-ms-input-placeholder {
  color: #bdbdbd;
  opacity: 0.5;
}
.main-content .all-steps .form-control::placeholder {
  color: #bdbdbd;
  opacity: 0.5;
}
.main-content .all-steps .billing-details-wrap {
  width: 500px;
}
.main-content .all-steps .input-group-text {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.main-content .all-steps .input-group-text img {
  height: 25px;
}
.main-content .profile-sidebar-title {
  font-family: "Lato", sans-serif;
  font-size: 18px;
  line-height: 100%;
  font-weight: 600;
  color: #fff;
  margin-bottom: 0;
}
.main-content .profile-sidebar-subtitle {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  font-family: "Lato", sans-serif;
  font-size: 16px;
  line-height: 100%;
  font-weight: 500;
  color: #dfdfdf;
  padding-right: 10px;
  margin-top: -3px;
  margin-bottom: 23px;
  position: relative;
}
.main-content .profile-sidebar-subtitle::after {
  content: "";
  position: absolute;
  top: 51%;
  left: 100%;
  width: 500px;
  height: 1px;
  background: #dfdfdf;
}
.main-content .profile-sidebar .top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 20px;
  padding-top: 25px;
  padding-bottom: 35px;
}
.main-content .profile-sidebar .top .image-wrap {
  position: relative;
}
.main-content .profile-sidebar .top .image-wrap .image-change {
  position: absolute;
  bottom: 0;
  -webkit-transform: translateY(15%);
          transform: translateY(15%);
  right: 25px;
  width: 35px;
  height: 35px;
  border: 0;
  border-radius: 50%;
  color: #fff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .profile-sidebar .top .image-wrap .image-change:hover {
  background: #8b97b2;
}
.main-content .profile-sidebar .top .part-img {
  width: 140px;
}
.main-content .profile-sidebar .top .part-txt {
  width: calc(100% - 160px);
}
.main-content .profile-sidebar .top .part-txt h4 {
  font-family: "Lato", sans-serif;
  font-size: 18px;
  line-height: 100%;
  font-weight: 600;
  color: #dfdfdf;
  margin-top: -2px;
  margin-bottom: 14px;
}
.main-content .profile-sidebar .top .part-txt span {
  display: inline-block;
  font-size: 16px;
  line-height: 100%;
  color: #A9B4CC;
  margin-bottom: 27px;
}
.main-content .profile-sidebar .top .part-txt .admin-social {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  font-size: 16px;
  line-height: 100%;
}
.main-content .profile-sidebar .top .part-txt .admin-social a {
  color: #dfdfdf;
}
.main-content .profile-sidebar .bottom {
  font-family: "Lato", sans-serif;
  overflow: hidden;
}
.main-content .profile-sidebar .bottom ul {
  margin-top: -13px;
  margin-bottom: 23px;
}
.main-content .profile-sidebar .bottom ul li {
  font-size: 14px;
  line-height: 35px;
  color: #e4e4e4;
}
.main-content .profile-sidebar .bottom ul li span {
  font-weight: 500;
  color: #A9B4CC;
  margin-right: 10px;
}
.main-content .profile-sidebar .bottom p {
  font-size: 16px;
  line-height: 26px;
  color: #A9B4CC;
  margin-top: -8px;
  margin-bottom: -7px;
}
.main-content .user-activity-title {
  display: block;
  font-size: 16px;
  line-height: 100%;
  margin-top: -1px;
  margin-bottom: 13px;
}
.main-content .user-activity-details {
  display: block;
  font-size: 14px;
  color: #A9B4CC;
  margin-bottom: 5px;
}
.main-content .user-activity-date {
  display: block;
  font-size: 12px;
  color: #A9B4CC;
  margin-bottom: -5px;
}
.main-content .user-activity-time {
  font-size: 14px;
  line-height: 100%;
  padding-left: 10px;
}
.main-content .user-activity ul li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  color: #dfdfdf;
  margin-bottom: 30px;
  z-index: 2;
}
.main-content .user-activity ul li:last-child {
  margin-bottom: 0;
}
.main-content .user-activity ul li:last-child .left::before {
  display: none;
}
.main-content .user-activity ul li::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  border-top: 1px dashed rgba(223, 223, 223, 0.15);
  z-index: -1;
}
.main-content .user-activity ul li .left {
  background: #112143;
  max-width: 350px;
  padding-right: 15px;
  padding-left: 20px;
  position: relative;
}
.main-content .user-activity ul li .left::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: calc(100% + 30px);
}
.main-content .user-activity ul li .left::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 17px;
  height: 17px;
  background: #e4e4e4;
  border-radius: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .user-activity ul li .right {
  background: #112143;
}
.main-content .profile-edit-tab-title {
  position: relative;
  z-index: 2;
}
.main-content .profile-edit-tab-title::after {
  content: "";
  position: absolute;
  top: 51%;
  left: 0;
  width: 100%;
  height: 1px;
  background: #dfdfdf;
  opacity: 0.3;
  z-index: -1;
}
.main-content .profile-edit-tab-title h6 {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  font-family: "Lato", sans-serif;
  font-size: 16px;
  line-height: 100%;
  font-weight: 500;
  color: #dfdfdf;
  background: #112143;
  padding-right: 10px;
  margin-top: -3px;
  margin-bottom: 23px;
}
.main-content .profile-edit-tab .admin-profile {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.main-content .profile-edit-tab .admin-profile .image-wrap {
  width: 140px;
  position: relative;
  margin-bottom: 30px;
}
.main-content .profile-edit-tab .admin-profile .image-wrap .image-change {
  position: absolute;
  bottom: 0;
  -webkit-transform: translateY(15%);
          transform: translateY(15%);
  right: 25px;
  width: 35px;
  height: 35px;
  border: 0;
  border-radius: 50%;
  color: #fff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .profile-edit-tab .admin-profile .image-wrap .image-change:hover {
  background: #8b97b2;
}
.main-content .profile-edit-tab .admin-profile .admin-name {
  display: block;
  font-family: "Lato", sans-serif;
  font-size: 18px;
  line-height: 100%;
  font-weight: 600;
  color: #dfdfdf;
  margin-top: -2px;
  margin-bottom: 14px;
}
.main-content .profile-edit-tab .admin-profile .admin-role {
  display: inline-block;
  font-size: 16px;
  line-height: 100%;
  color: #A9B4CC;
  margin-bottom: -3px;
}
.main-content .file-manager-sidebar {
  color: #c8d4f0;
}
.main-content .file-manager-sidebar .connected-app {
  padding: 10px 0;
}
.main-content .file-manager-sidebar .connected-app li {
  line-height: 35px;
  font-size: 14px;
  height: 35px;
}
.main-content .file-manager-sidebar .connected-app li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  color: #c8d4f0;
}
.main-content .file-manager-sidebar .connected-app li a:hover {
  color: #F9F9F9;
}
.main-content .file-manager-sidebar .connected-app li i {
  text-align: center;
  width: 18px;
  font-size: 16px;
}
.main-content .file-manager-sidebar .connected-app .file-manager-sidebar-title {
  font-size: 16px;
  color: #fff;
}
.main-content .file-manager-sidebar .other-files {
  width: 100%;
  padding: 10px 0;
}
.main-content .file-manager-sidebar nav button {
  border: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 14px;
  line-height: 100%;
  padding: 0;
  height: 35px;
  background: transparent;
  color: #c8d4f0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .file-manager-sidebar nav button i {
  width: 18px;
  text-align: center;
  font-size: 16px;
}
.main-content .file-manager-sidebar nav button.all-files-tab {
  height: 50px;
  line-height: 50px;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  font-size: 16px;
}
.main-content .file-manager-sidebar nav button.active {
  color: #F9F9F9;
}
.main-content .file-manager-sidebar .file-category-status {
  padding-top: 20px;
}
.main-content .file-manager-sidebar .file-category-status li {
  margin-bottom: 20px;
}
.main-content .file-manager-sidebar .file-category-status li:last-child {
  margin-bottom: 0;
}
.main-content .file-manager-sidebar .progress-txt {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 10px;
}
.main-content .file-manager-sidebar .progress-txt .file-category-name {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 5px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  line-height: 100%;
}
.main-content .file-manager-sidebar .progress-txt .file-category-name i {
  width: 18px;
}
.main-content .file-manager-sidebar .progress-txt .file-category-name p {
  font-size: 14px;
  line-height: 100%;
  margin-bottom: -2px;
}
.main-content .file-manager-sidebar .progress-txt .using-storage {
  font-size: 14px;
  line-height: 100%;
  margin-bottom: -2px;
}
.main-content .file-manager-sidebar .progress {
  height: 5px;
}
.main-content .file-manager-sidebar .progress-stacked {
  height: 5px;
  margin-bottom: 9px;
}
.main-content .file-manager-sidebar .storage-status .progress {
  margin-bottom: 9px;
}
.main-content .file-manager-sidebar .storage-status p {
  font-size: 14px;
  line-height: 100%;
  margin-bottom: -2px;
}
.main-content .file-manager-card {
  display: block;
  background: #061535;
  border-radius: 3px;
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  padding: 15px;
  color: #c8d4f0;
}
.main-content .file-manager-card .top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.main-content .file-manager-card .top .part-icon {
  font-size: 18px;
  line-height: 100%;
  color: #F0C434;
}
.main-content .file-manager-card .top .dropdown {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.main-content .file-manager-card .top .action {
  background: transparent;
  padding: 0;
  border: 0;
  color: #F9F9F9;
  line-height: 100%;
  font-size: 20px;
}
.main-content .file-manager-card .top .dropdown-menu {
  top: 100% !important;
  left: auto !important;
  right: 0 !important;
  min-width: 150px;
  padding: 5px 0;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}
.main-content .file-manager-card .top .dropdown-menu.show {
  -webkit-transform: translate(0) !important;
          transform: translate(0) !important;
}
.main-content .file-manager-card .top .dropdown-menu .dropdown-item {
  padding: 7px 10px;
}
.main-content .file-manager-card .top .dropdown-menu .dropdown-item:active, .main-content .file-manager-card .top .dropdown-menu .dropdown-item:focus, .main-content .file-manager-card .top .dropdown-menu .dropdown-item:hover {
  background: transparent;
  text-shadow: 0px 0px 10px rgba(17, 33, 67, 0.4);
}
.main-content .file-manager-card .bottom {
  padding-top: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}
.main-content .file-manager-card .bottom span {
  display: block;
  font-size: 14px;
  line-height: 100%;
  margin-top: -1px;
  margin-bottom: -2px;
}
.main-content .file-manager-card .bottom .left .folder-name {
  display: block;
  font-size: 14px;
  line-height: 100%;
  font-weight: 500;
  color: #F9F9F9;
  margin-top: -1px;
  margin-bottom: 8px;
  background: transparent;
  padding: 0;
  border: 0;
}
.main-content .file-manager-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 20px;
}
.main-content .file-manager-row .file-manager-col-head {
  width: 100%;
  background: #071739;
  border-radius: 3px 3px 0 0;
  padding: 14px 15px;
  display: none;
}
.main-content .file-manager-row .file-manager-col-head span {
  display: block;
  width: 20%;
  color: #e4e4e4;
  font-size: 14px;
  line-height: 100%;
  text-align: center;
}
.main-content .file-manager-row .file-manager-col-head span:first-child {
  width: 15%;
  text-align: left;
}
.main-content .file-manager-row .file-manager-col-head span:nth-child(2), .main-content .file-manager-row .file-manager-col-head span:nth-child(3), .main-content .file-manager-row .file-manager-col-head span:nth-child(4) {
  width: 23.**********%;
}
.main-content .file-manager-row .file-manager-col-head span:last-child {
  width: 15%;
}
.main-content .file-manager-row .file-manager-col {
  width: calc(20% - 16px);
}
.main-content .file-manager-row.list-view {
  gap: 0;
}
.main-content .file-manager-row.list-view .file-manager-col-head {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.main-content .file-manager-row.list-view .file-manager-col {
  width: 100%;
}
.main-content .file-manager-row.list-view .file-manager-col:last-child .file-card {
  border-radius: 0 0 3px 3px;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 5px 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .part-img {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  margin-right: auto;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .part-img img {
  max-width: none;
  min-width: 100%;
  min-height: 100%;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .part-txt {
  width: 46.6666666667%;
  padding: 0;
  border: 0;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .part-txt .file-name {
  width: 50%;
  text-align: center;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .part-txt .file-size {
  width: 50%;
  text-align: center;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .file-type {
  display: block;
  width: 23.**********%;
  text-align: center;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .action {
  top: 0;
  right: 0;
  position: relative;
  width: 15%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .action .btn-flush {
  display: none;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .action .btn {
  display: block;
}
.main-content .file-manager-row.list-view .file-manager-col .file-card .action .dropdown-menu {
  -webkit-animation: none;
          animation: none;
}
.main-content .file-card {
  background: #0a1735;
  border-radius: 3px;
  position: relative;
}
.main-content .file-card .part-img {
  width: 100%;
  height: 150px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 3px 3px 0 0;
  overflow: hidden;
}
.main-content .file-card .part-img button {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.main-content .file-card .part-img button img {
  max-height: 100%;
}
.main-content .file-card .part-img .file-icon {
  width: 40px;
}
.main-content .file-card .part-txt {
  padding: 5px 10px;
  color: #c8d4f0;
  font-size: 14px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0 0 3px 3px;
}
.main-content .file-card .part-txt .file-name {
  text-align: left;
  width: 60%;
  font-size: 14px;
  color: #c8d4f0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 20px;
}
.main-content .file-card .part-txt .file-size {
  width: 40%;
  text-align: right;
}
.main-content .file-card .file-type {
  display: none;
  font-size: 14px;
  line-height: 100%;
  color: #c8d4f0;
}
.main-content .file-card .action {
  position: absolute;
  top: 10px;
  right: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.main-content .file-card .action .btn {
  display: none;
}
.main-content .file-card .action .btn-star {
  margin-right: 15px;
}
.main-content .file-card .action .btn-flush {
  font-size: 16px;
  line-height: 100%;
}
.main-content .file-card .action .dropdown-menu {
  left: auto !important;
  right: 0 !important;
  min-width: 150px;
  padding: 5px 0;
}
.main-content .file-card .action .dropdown-menu .dropdown-item {
  padding: 7px 10px;
}
.main-content .file-card .action .dropdown-menu .dropdown-item:active, .main-content .file-card .action .dropdown-menu .dropdown-item:focus, .main-content .file-card .action .dropdown-menu .dropdown-item:hover {
  background: transparent;
  text-shadow: 0px 0px 10px rgba(17, 33, 67, 0.4);
}
.main-content .settings-nav {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.main-content .settings-nav button {
  background: transparent;
  padding: 0 10px;
  border: 0;
  height: 40px;
  line-height: 40px;
  border-radius: 3px 3px 0 0;
  color: #c8d4f0;
  font-family: "Lato", sans-serif;
  text-transform: capitalize;
  font-size: 16px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .settings-nav button.active {
  background: #071739;
  background: rgba(255, 255, 255, 0.1);
  color: #F9F9F9;
}
.main-content .file-manager-user {
  padding: 25px 0 20px;
}
.main-content .file-manager-user .avatar {
  width: 44px;
  height: 44px;
  margin-bottom: 15px;
}
.main-content .file-manager-user .avatar img {
  border-radius: 22px;
}
.main-content .file-manager-user .part-txt {
  color: #c4c4c4;
  margin-bottom: -7px;
}
.main-content .file-manager-user .part-txt span {
  display: inline-block;
  font-size: 16px;
  line-height: 100%;
}
.main-content .file-manager-user .part-txt .name {
  margin-bottom: 2px;
}
.main-content .file-manager-user .part-txt .email span {
  font-size: 14px;
}
.main-content .file-manager-user .part-txt .btn-flush {
  font-size: 14px;
}
.main-content .file-manager-settings-subtitle {
  font-family: "Lato", sans-serif;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 1px;
  color: #F7F7F7;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 4px;
  margin-bottom: 20px;
}
.main-content .about-plan {
  margin-bottom: 25px;
}
.main-content .plan-name {
  font-size: 20px;
  line-height: 100%;
  font-weight: 400;
  color: #c4c4c4;
  margin-bottom: 15px;
}
.main-content .used-storage {
  display: block;
  font-family: "Lato", sans-serif;
  font-size: 14px;
  line-height: 100%;
  color: #c4c4c4;
  margin-bottom: -2px;
}
.main-content .file-manager-settings-table {
  color: #c4c4c4;
  font-size: 14px;
  margin-top: -14px;
}
.main-content .file-manager-settings-table td {
  width: 33.**********%;
  padding: 10px;
}
.main-content .file-manager-settings-table td:last-child {
  text-align: right;
}
.main-content .file-manager-settings-table .btn-flush {
  font-size: 14px;
}
.main-content .part-btn {
  padding-top: 20px;
}
.main-content .part-btn p {
  font-size: 14px;
  line-height: 100%;
  color: #c4c4c4;
  margin-top: -1px;
  margin-bottom: 18px;
}
.main-content .perma-txt {
  padding-top: 15px;
  font-size: 14px;
  line-height: 100%;
  color: #97a3bb;
  margin-bottom: 0;
}
.main-content .perma-txt .site-link {
  font-family: "Lato", sans-serif;
}
.main-content .perma-txt input {
  font-family: "Lato", sans-serif;
  width: 160px;
  display: inline-block;
}
.main-content .perma-txt .btn-flush {
  font-size: 12px;
  padding: 1px 8px;
  border-radius: 2px;
}
.main-content .product-image-upload {
  color: #fff;
}
.main-content .product-image-upload.product-image-upload-2 .part-txt {
  margin-bottom: 15px;
}
.main-content .product-image-upload.product-image-upload-2 .part-txt h5 {
  font-size: 14px;
  margin-bottom: 1px;
}
.main-content .product-image-upload.product-image-upload-2 .jquery-uploader-select-card,
.main-content .product-image-upload.product-image-upload-2 .jquery-uploader-card {
  width: 150px;
  height: 150px;
}
.main-content .product-image-upload .part-txt {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 12px;
  margin-bottom: 20px;
}
.main-content .product-image-upload .part-txt h5 {
  font-family: "Lato", sans-serif;
  font-size: 18px;
  line-height: 100%;
  font-weight: 500;
  color: #c4c4c4;
  margin-top: -2px;
  margin-bottom: 6px;
}
.main-content .product-image-upload .part-txt h5 span {
  font-size: 14px;
  line-height: 100%;
  color: #8b97b2;
}
.main-content .jquery-uploader-preview-container {
  background: transparent;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.main-content .jquery-uploader-preview-container .jquery-uploader-select-card,
.main-content .jquery-uploader-preview-container .jquery-uploader-card {
  margin: 0;
  width: 200px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .jquery-uploader-preview-container .jquery-uploader-select-card i,
.main-content .jquery-uploader-preview-container .jquery-uploader-card i {
  color: inherit;
}
.main-content .jquery-uploader-preview-container .jquery-uploader-select-card .upload-button,
.main-content .jquery-uploader-preview-container .jquery-uploader-card .upload-button {
  height: auto;
  font-family: "Lato", sans-serif;
  font-size: 12px;
  color: #97a3bb;
}
.main-content .jquery-uploader-preview-container .jquery-uploader-select-card .upload-button i,
.main-content .jquery-uploader-preview-container .jquery-uploader-card .upload-button i {
  font-size: 25px;
  margin-bottom: 10px;
}
.main-content .jquery-uploader-preview-container .jquery-uploader-select-card:hover,
.main-content .jquery-uploader-preview-container .jquery-uploader-card:hover {
  border-color: rgba(255, 255, 255, 0.5);
}
.main-content .jquery-uploader-preview-container .jquery-uploader-preview-action {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.main-content .jquery-uploader-preview-container .jquery-uploader-preview-action ul {
  padding: 0;
}
.main-content .jquery-uploader-preview-container .jquery-uploader-preview-action ul li {
  font-size: 25px;
  padding: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .jquery-uploader-preview-container .jquery-uploader-preview-action ul li i {
  color: inherit;
}
.main-content .jquery-uploader-preview-container .jquery-uploader-preview-action ul li:hover {
  -webkit-transform: scale(1);
          transform: scale(1);
  color: #f00000;
}
.main-content .add-product-attribute .select2-container {
  width: 100% !important;
}
.main-content .product-data-tab label {
  color: #c8d4f0;
}
.main-content .product-data-tab .input-additional-txt {
  display: block;
  font-family: "Lato", sans-serif;
  font-size: 14px;
  line-height: 100%;
  color: #8b97b2;
  padding-top: 8px;
  margin-bottom: -2px;
}
.main-content .publish-date {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
  color: #fff;
  margin-top: -4px;
  margin-bottom: 14px;
}
.main-content .publish-date label {
  font-size: 14px;
}
.main-content .publish-date input {
  font-size: 14px;
  color: #c8d4f0;
}
.main-content .product-categories .form-check {
  margin-top: 20px;
}
.main-content .product-categories .form-check label {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.main-content .product-categories .sub-cat-group {
  padding-left: 20px;
}
.main-content .add-new-category-panel {
  padding-top: 20px;
}
.main-content .product-tag-area .input-txt {
  display: block;
  font-family: "Lato", sans-serif;
  font-size: 14px;
  line-height: 100%;
  color: #97a3bb;
  padding-top: 13px;
  margin-bottom: -2px;
}
.main-content .product-tag-area .input-txt.choose-used-tag {
  cursor: pointer;
}
.main-content .product-tag-area .all-tags {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.main-content .product-tag-area .all-tags.active {
  padding-top: 15px;
}
.main-content .product-tag-area .all-tags .item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  font-family: "Lato", sans-serif;
  color: #97a3bb;
  font-size: 14px;
  height: 25px;
  line-height: 24px;
  padding: 0 10px;
  position: relative;
}
.main-content .product-tag-area .all-tags .item .close-tag {
  position: absolute;
  top: -4px;
  right: -4px;
  display: inline-block;
  text-align: center;
  color: #e9f1ff;
  width: 12px;
  height: 12px;
  line-height: 12px;
  background: #f00000;
  border-radius: 50%;
  font-size: 10px;
  margin-left: 10px;
  cursor: pointer;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  opacity: 0;
}
.main-content .product-tag-area .all-tags .item .close-tag i {
  margin-left: -0.5px;
}
.main-content .product-tag-area .all-tags .item:hover .close-tag {
  opacity: 1;
}
.main-content .product-tag-area .used-tags .item {
  cursor: pointer;
}
.main-content .product-tag-area .used-tags .item .close-tag {
  display: none;
}
.main-content .digi-dataTable {
  max-width: 100%;
  width: 100% !important;
  font-family: "Lato", sans-serif;
  border-bottom: 0 !important;
  color: #c8d4f0;
  font-size: 14px;
  vertical-align: middle;
}
.main-content .digi-dataTable.attendance-table thead th {
  padding-top: 12px;
  padding-bottom: 12px;
}
.main-content .digi-dataTable.attendance-table thead th:last-child {
  text-align: right;
}
.main-content .digi-dataTable.attendance-table tr td {
  padding: 12px 15px 13px 10px;
}
.main-content .digi-dataTable:not(.table-borderless) tr th {
  border-color: rgba(223, 223, 223, 0.15);
  border-bottom: 1px dashed rgba(223, 223, 223, 0.15);
}
.main-content .digi-dataTable:not(.table-borderless) tr td {
  border-color: rgba(223, 223, 223, 0.15);
  border-bottom: 1px dashed rgba(223, 223, 223, 0.15);
}
.main-content .digi-dataTable:not(.table-borderless) tr td:first-child {
  border-left: 1px dashed rgba(223, 223, 223, 0.15);
}
.main-content .digi-dataTable:not(.table-borderless) tr td:last-child {
  border-right: 1px dashed rgba(223, 223, 223, 0.15);
}
.main-content .digi-dataTable.table-borderless tr th {
  border-bottom: 0;
}
.main-content .digi-dataTable.dataTable-resize thead tr {
  height: auto !important;
}
.main-content .digi-dataTable thead {
  color: #f7faff;
  background: #071739;
}
.main-content .digi-dataTable thead th {
  font-weight: 400;
  padding: 13px 28px 13px 10px;
  background-position-x: calc(100% - 10px);
  background-size: 13px;
}
.main-content .digi-dataTable thead th.no-sort {
  padding-right: 13px;
  background-image: none !important;
}
.main-content .digi-dataTable tbody tr.selected {
  background: #0a1735;
}
.main-content .digi-dataTable tr {
  background: transparent;
}
.main-content .digi-dataTable tr.odd > * {
  color: #c8d4f0;
}
.main-content .digi-dataTable tr th {
  vertical-align: middle;
}
.main-content .digi-dataTable tr td {
  vertical-align: middle;
  padding: 14px 15px 15px 10px;
}
.main-content .digi-dataTable tr td:first-child {
  text-align: left;
}
.main-content .digi-dataTable tr td:last-child {
  text-align: right;
}
.main-content .digi-dataTable .btn-box {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  margin-left: auto;
  gap: 15px;
}
.main-content .digi-dataTable .btn-box button:not(.btn) {
  padding: 0;
  background: transparent;
  border: 0;
  color: #c8d4f0;
  font-size: 16px;
  line-height: 100%;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .digi-dataTable .btn-box button:not(.btn):hover, .main-content .digi-dataTable .btn-box button:not(.btn):focus {
  color: #fff;
}
.main-content .table-product-card {
  width: 100%;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
}
.main-content .table-product-card .part-img {
  width: 55px;
  height: 55px;
  border-radius: 3px;
  overflow: hidden;
}
.main-content .table-product-card .part-txt {
  max-width: 277px;
  text-align: left;
}
.main-content .table-product-card .part-txt span {
  display: block;
}
.main-content .table-product-card .part-txt span.product-name {
  font-size: 14px;
  line-height: 100%;
  color: #F9F9F9;
  margin-top: -2px;
  margin-bottom: 7px;
}
.main-content .table-product-card .part-txt span.product-category {
  margin-bottom: -5px;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .all-product-table th, .main-content .all-product-table td {
  text-align: center;
}
.main-content tr:hover .table-product-card .part-txt .product-category {
  opacity: 1;
}
.main-content .rating {
  text-align: center;
}
.main-content .rating .star {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  gap: 3px;
  font-size: 10px;
}
.main-content .rating .star .starred {
  color: #F0C434;
}
.main-content .product-table-quantity ul {
  font-family: "Lato", sans-serif;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
  color: #c8d4f0;
  font-size: 14px;
  line-height: 100%;
  margin-top: -2px;
  margin-bottom: 18px;
}
.main-content .product-table-quantity ul.top-action {
  gap: 18px;
}
.main-content .product-table-quantity ul.top-action button {
  font-size: 14px;
}
.main-content .table-filter-option {
  margin-bottom: 20px;
}
.main-content .table-filter-option .dataTables_length label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  color: #c8d4f0;
}
.main-content .upload-category-thumbnail #addCatThumb {
  cursor: pointer;
}
.main-content .upload-category-thumbnail .jquery-uploader-preview-container .jquery-uploader-select-card, .main-content .upload-category-thumbnail .jquery-uploader-preview-container .jquery-uploader-card {
  width: 100px;
  height: 100px;
}
.main-content .upload-category-thumbnail .jquery-uploader {
  display: none;
  padding-top: 13px;
}
.main-content .table-category-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 20px;
}
.main-content .table-category-card .part-icon span {
  display: block;
  text-align: center;
  width: 50px;
  height: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  font-size: 35px;
}
.main-content .table a {
  color: #c8d4f0;
  text-decoration: underline;
}
.main-content .table-dscr {
  text-align: left;
  display: inline-block;
  max-width: 350px;
}
.main-content .invoice {
  color: #c8d4f0;
  padding: 30px;
}
.main-content .invoice .invoice-header .shop-address {
  font-family: "Lato", sans-serif;
  font-size: 16px;
  line-height: 1.3;
  letter-spacing: 0.5px;
}
.main-content .invoice .invoice-header .shop-address p {
  margin-bottom: 6px !important;
}
.main-content .invoice .invoice-header .shop-address p:last-child {
  margin-bottom: -5px !important;
}
.main-content .invoice .info-card h3 {
  font-size: 20px;
  font-weight: 500;
  line-height: 100%;
  color: #fff;
  margin-top: -2px;
  margin-bottom: 11px;
}
.main-content .invoice .info-card ul {
  margin-bottom: -6px;
}
.main-content .invoice .info-card ul li {
  font-family: "Lato", sans-serif;
  font-size: 14px;
  line-height: 1.6;
}
.main-content .invoice .info-card ul li span {
  font-weight: 600;
  margin-right: 3px;
}
.main-content .invoice .table {
  font-size: 14px;
  letter-spacing: 0.5px;
  color: #c8d4f0;
}
.main-content .invoice .table th {
  font-weight: 600;
  padding: 10px;
}
.main-content .invoice .table td {
  padding: 10px;
}
.main-content .invoice .total-payment-area ul {
  margin-top: -9px;
  padding: 0;
  margin-bottom: 0;
}
.main-content .invoice .total-payment-area ul li {
  font-size: 16px;
  line-height: 2;
}
.main-content .invoice .total-payment-area ul li:last-child {
  color: #fff;
}
.main-content .invoice .invoice-note {
  text-align: center;
  font-size: 14px;
  line-height: 1.5;
  margin-top: -15px;
  margin-bottom: -6px !important;
}
.main-content .chatting-panel {
  position: absolute;
  width: calc(100% - 50px);
  height: calc(100% - 95px);
  border-radius: 3px;
  overflow: hidden;
}
.main-content .chatting-panel > * {
  height: 100%;
}
.main-content .chatting-panel .panel {
  height: 100%;
}
.main-content .chatting-panel .panel:first-child, .main-content .chatting-panel .panel:last-child {
  width: 318px;
}
.main-content .chatting-panel .panel:nth-child(2) {
  width: calc(100% - 636px);
}
.main-content .chatting-panel .active-members .owl-stage-outer {
  overflow-y: visible;
  overflow-x: clip;
}
.main-content .chatting-panel .active-members .owl-nav {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: #c8d4f0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  visibility: hidden;
}
.main-content .chatting-panel .active-members .owl-nav button {
  font-size: 18px;
  visibility: visible;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  background: -webkit-gradient(linear, left top, right top, color-stop(30%, #112143), to(transparent));
  background: linear-gradient(90deg, #112143 30%, transparent);
  height: 36px;
  padding-right: 15px !important;
}
.main-content .chatting-panel .active-members .owl-nav button.owl-next {
  padding-left: 15px !important;
  padding-right: 0 !important;
  background: -webkit-gradient(linear, right top, left top, color-stop(30%, #112143), to(transparent));
  background: linear-gradient(-90deg, #112143 30%, transparent);
}
.main-content .chatting-panel .active-members .owl-nav button:hover {
  color: #fff;
}
.main-content .chatting-panel .active-members .single-member {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: relative;
}
.main-content .chatting-panel .avatar {
  position: relative;
  max-width: 35px;
  max-height: 35px;
}
.main-content .chatting-panel .avatar.avatar-lg {
  max-width: none;
  max-height: none;
}
.main-content .chatting-panel .avatar .active-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 9px;
  height: 9px;
  background: #6c757d;
  border: 1px solid #e4e4e4;
  border-radius: 50%;
}
.main-content .chatting-panel .avatar .active-status.active {
  background: #2d893a;
}
.main-content .chatting-panel .message-list {
  height: calc(100% - 75px);
}
.main-content .chatting-panel .message-list .single-message {
  background: rgba(255, 255, 255, 0.06);
  border-radius: 3px;
  padding: 15px;
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
  color: #c8d4f0;
  cursor: pointer;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .chatting-panel .message-list .single-message.unread {
  position: relative;
}
.main-content .chatting-panel .message-list .single-message.unread::after {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  width: 6px;
  height: 6px;
  background: #f00000;
  border-radius: 50%;
}
.main-content .chatting-panel .message-list .single-message .part-txt {
  width: calc(100% - 50px);
}
.main-content .chatting-panel .message-list .single-message .part-txt .top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-top: -2px;
  margin-bottom: 5px;
}
.main-content .chatting-panel .message-list .single-message .part-txt .user-name {
  font-size: 14px;
  line-height: 100%;
  color: #fff;
}
.main-content .chatting-panel .message-list .single-message .part-txt .msg-time {
  font-size: 12px;
  line-height: 100%;
}
.main-content .chatting-panel .message-list .single-message .part-txt .msg-short {
  font-family: "Lato", sans-serif;
  font-size: 13px;
  line-height: 1.3;
}
.main-content .chatting-panel .message-list .single-message .part-txt .msg-short span {
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: -4px;
}
.main-content .chatting-panel .message-list .single-message:hover {
  background: rgba(255, 255, 255, 0.1);
}
.main-content .chatting-panel .user-short {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.main-content .chatting-panel .user-short .avatar {
  margin-bottom: 20px;
}
.main-content .chatting-panel .user-short .part-txt {
  color: #c8d4f0;
  text-align: center;
  margin-bottom: 20px;
}
.main-content .chatting-panel .user-short .part-txt span {
  display: block;
}
.main-content .chatting-panel .user-short .part-txt span.user-name {
  display: block;
  font-size: 20px;
  line-height: 100%;
  font-weight: 500;
  margin-top: -2px;
  margin-bottom: 11px;
}
.main-content .chatting-panel .user-short .part-txt span.user-mail {
  display: block;
  font-size: 14px;
  line-height: 100%;
  margin-bottom: -2px;
}
.main-content .chatting-panel .user-short .user-option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
}
.main-content .chatting-panel .chatting-option {
  background: rgba(255, 255, 255, 0.06);
  border-radius: 3px;
  padding: 20px;
}
.main-content .chatting-panel .chatting-option ul li {
  font-size: 16px;
  line-height: 100%;
  margin-bottom: 20px;
}
.main-content .chatting-panel .chatting-option ul li:last-child {
  margin-bottom: 0;
}
.main-content .chatting-panel .chatting-option ul li a {
  color: #c8d4f0;
}
.main-content .chatting-panel .chatting-option ul li a span {
  display: inline-block;
  width: 20px;
  text-align: center;
  margin-right: 10px;
}
.main-content .chatting-panel .chatting-option ul li a:hover {
  color: #fff;
}
.main-content .chatting-panel .chatting-area {
  height: 100%;
}
.main-content .chatting-panel .chatting-area .chat-top-bar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  position: relative;
}
.main-content .chatting-panel .chatting-area .chat-top-bar .user {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
}
.main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt {
  color: #c8d4f0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 20px;
}
.main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .user-name {
  font-size: 16px;
  font-weight: 500;
  line-height: 100%;
}
.main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .active-status {
  font-family: "Lato", sans-serif;
  font-size: 12px;
  line-height: 100%;
  padding-left: 13px;
  position: relative;
}
.main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .active-status::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: #6c757d;
  border: 1px solid #fff;
  border-radius: 50%;
}
.main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .active-status.active::after {
  background: #2d893a;
}
.main-content .chatting-panel .chatting-area .chat-top-bar .search-in-chat {
  position: absolute;
  top: -50%;
  left: 10px;
  right: 10px;
  padding: 10px;
  background: #112143;
  border-radius: 3px;
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
  z-index: 3;
}
.main-content .chatting-panel .chatting-area .chat-top-bar .search-in-chat.active {
  top: calc(100% + 30px);
  opacity: 1;
  visibility: visible;
}
.main-content .chatting-panel .msg-area {
  height: calc(100% - 150px);
  background: #071739;
  z-index: 2;
  position: relative;
}
.main-content .chatting-panel .msg-area::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(../images/chat-bg.png);
  z-index: -1;
}
.main-content .chatting-panel .msg-area .os-viewport {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.main-content .chatting-panel .msg-area .os-viewport .os-content {
  height: -webkit-max-content !important;
  height: -moz-max-content !important;
  height: max-content !important;
  max-height: 100% !important;
}
.main-content .chatting-panel .msg-area .day-divider {
  text-align: center;
  margin-top: -7px;
  margin-bottom: 13px;
  position: relative;
  overflow: hidden;
  z-index: 2;
}
.main-content .chatting-panel .msg-area .day-divider span {
  display: inline-block;
  color: #c8d4f0;
  font-family: "Lato", sans-serif;
  font-size: 14px;
  line-height: 100%;
  position: relative;
  padding: 0 5px;
}
.main-content .chatting-panel .msg-area .day-divider span::before {
  content: "";
  position: absolute;
  top: 50%;
  right: 100%;
  width: 9999px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  z-index: -1;
}
.main-content .chatting-panel .msg-area .day-divider span::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 100%;
  width: 9999px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  z-index: -1;
}
.main-content .chatting-panel .msg-area .single-message {
  max-width: 80%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  gap: 15px;
  margin-bottom: 20px;
}
.main-content .chatting-panel .msg-area .single-message:last-child {
  margin-bottom: 0;
}
.main-content .chatting-panel .msg-area .single-message.outgoing {
  margin-left: auto;
}
.main-content .chatting-panel .msg-area .single-message.outgoing .msg-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}
.main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner {
  background: #112143;
}
.main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner .msg-option {
  left: auto;
  right: calc(100% + 20px);
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}
.main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner .sent-status {
  position: absolute;
  bottom: 0;
  right: calc(100% + 5px);
  color: #c8d4f0;
  font-size: 12px;
  line-height: 100%;
  opacity: 0.5;
}
.main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner .sent-status.seen {
  opacity: 1;
}
.main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner p {
  color: #c8d4f0;
}
.main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner:last-child {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 0;
}
.main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner:last-child::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: auto;
  left: 100%;
  border-bottom: 10px solid #112143;
  border-right: 10px solid transparent;
  border-left: 0;
}
.main-content .chatting-panel .msg-area .single-message .msg-box {
  max-width: calc(100% - 50px);
}
.main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner {
  display: inline-block;
  padding: 15px;
  border-radius: 3px;
  margin-bottom: 5px;
  position: relative;
}
.main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner:hover .msg-option {
  opacity: 1;
}
.main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option {
  position: absolute;
  top: 50%;
  left: calc(100% + 10px);
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  gap: 10px;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option .msg-time {
  font-style: italic;
  font-size: 12px;
  height: 10px;
  line-height: 10px;
  color: #fff;
  opacity: 0.5;
}
.main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option button {
  height: 14px;
  font-size: 16px;
  line-height: 14px;
  opacity: 0.5;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option button:hover {
  opacity: 1;
}
.main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner p {
  font-family: "Lato", sans-serif;
  font-size: 13px;
  letter-spacing: 0.5px;
  color: #F9F9F9;
  margin-top: -4px;
  margin-bottom: -5px;
}
.main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner:last-child {
  margin-bottom: 0;
  border-bottom-left-radius: 0;
  position: relative;
}
.main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner:last-child::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 100%;
  border-bottom: 10px solid transparent;
  border-left: 10px solid transparent;
}
.main-content .chatting-panel .msg-type-area {
  position: absolute;
  bottom: 0;
  width: 100%;
  color: #c8d4f0;
}
.main-content .chatting-panel .msg-type-area form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
}
.main-content .chatting-panel .msg-type-area form .chat-input {
  width: calc(100% - 82px);
}
.main-content .chatting-panel .msg-type-area form .chat-attachment {
  display: none;
}
.main-content .email-panel {
  position: absolute;
  width: calc(100% - 50px);
  height: calc(100% - 95px);
  border-radius: 3px;
  overflow: hidden;
}
.main-content .email-panel > * {
  height: 100%;
}
.main-content .email-panel > .panel {
  height: 100%;
}
.main-content .email-panel > .panel:first-child {
  width: 230px;
}
.main-content .email-panel > .panel:nth-child(2) {
  width: calc(100% - 230px);
}
.main-content .email-panel > .panel .panel-body:not(.email-menu) {
  height: calc(100% - 60px);
}
.main-content .email-panel .tab-content {
  height: 100%;
}
.main-content .email-panel .tab-pane {
  height: 100%;
}
.main-content .email-panel .tab-pane .tab-content {
  height: calc(100% - 145px);
}
.main-content .email-panel .tab-pane .tab-content .table-wrapper {
  height: 100%;
}
.main-content .email-panel .email-menu {
  height: 100%;
}
.main-content .email-panel .emial-menu-list {
  margin-bottom: 15px;
}
.main-content .email-panel .emial-menu-list ul {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  border-collapse: collapse;
}
.main-content .email-panel .emial-menu-list ul li {
  width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 0;
  overflow: hidden;
}
.main-content .email-panel .emial-menu-list ul li:first-child {
  border-top-left-radius: 3px;
}
.main-content .email-panel .emial-menu-list ul li:nth-child(2) {
  border-top-right-radius: 3px;
}
.main-content .email-panel .emial-menu-list ul li:nth-last-child(2) {
  border-bottom-left-radius: 3px;
}
.main-content .email-panel .emial-menu-list ul li:nth-last-child(1) {
  border-bottom-right-radius: 3px;
}
.main-content .email-panel .emial-menu-list ul li:nth-last-child(2), .main-content .email-panel .emial-menu-list ul li:nth-last-child(1) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.main-content .email-panel .emial-menu-list ul li:nth-child(even) {
  border-left-width: 0;
}
.main-content .email-panel .emial-menu-list ul li button {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 20px 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  position: relative;
}
.main-content .email-panel .emial-menu-list ul li button .badge {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 0 4px;
  min-width: 17px;
  height: 17px;
  line-height: 17px;
}
.main-content .email-panel .emial-menu-list ul li button .part-icon {
  display: block;
  font-size: 18px;
  line-height: 100%;
  color: #fff;
  margin-bottom: 13px;
}
.main-content .email-panel .emial-menu-list ul li button .part-txt {
  font-size: 14px;
  line-height: 100%;
  color: #c8d4f0;
  margin-bottom: -1px;
}
.main-content .email-panel .emial-menu-list ul li button:hover .part-txt, .main-content .email-panel .emial-menu-list ul li button.active .part-txt {
  color: #fff;
}
.main-content .email-panel .new-label-list button {
  font-size: 14px;
  margin-top: 10px;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
}
.main-content .email-panel .new-label-list button .part-icon {
  margin-right: 7px;
}
.main-content .email-panel .new-label-list button .part-txt {
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.main-content .email-panel .table-wrapper {
  height: calc(100% - 143px);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.main-content .email-panel .table-wrapper.mail-opened .mail-list {
  width: 55%;
}
.main-content .email-panel .table-wrapper .mail-list {
  height: 100%;
  width: 100%;
}
.main-content .email-panel .table-wrapper .dataTables_wrapper {
  height: 100%;
}
.main-content .email-panel .table-wrapper .dataTables_scroll {
  height: 100%;
}
.main-content .email-panel .table-wrapper .dataTables_scrollBody {
  min-height: calc(100% - 48px);
}
.main-content .email-panel .email-table {
  min-width: auto;
}
.main-content .email-panel .email-table td, .main-content .email-panel .email-table th {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  text-align: left;
  cursor: pointer;
}
.main-content .email-panel .email-table td .table-txt, .main-content .email-panel .email-table th .table-txt {
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.main-content .email-panel .email-table td {
  padding-right: 10px;
}
.main-content .email-panel .email-table .unread .table-txt {
  font-weight: 600;
  color: #fff;
}
.main-content .email-panel .email-table .btn-star {
  margin-right: 0;
}
.main-content .email-panel .table-bottom-control {
  background: #071739;
}
.main-content .upcoming-event-list a {
  margin: 0 0 10px;
}
.main-content .upcoming-event-list a:last-child {
  margin-bottom: 0;
}
.main-content .map-panel {
  margin-bottom: -7px;
}
.main-content .map-panel iframe {
  width: 100%;
  height: 400px;
}
.main-content .all-employee-table th:not(:last-child), .main-content .all-employee-table td {
  text-align: left;
}
.main-content .all-employee-table .address-txt {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.main-content .leads-table th, .main-content .leads-table td {
  text-align: left;
}
.main-content .leads-table tr td:last-child {
  text-align: left;
}
.main-content .task-table th:not(:last-child), .main-content .task-table td {
  text-align: left;
}
.main-content .company-table th, .main-content .company-table td,
.main-content .target-audience-table th,
.main-content .target-audience-table td {
  text-align: left;
}
.main-content .icon-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 20px;
}
.main-content .icon-row .icon-col {
  width: calc(11.1111111111% - 17.8px);
}
.main-content .icon-row .icon-col .icon-box {
  border: 1px dashed rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  text-align: center;
  padding: 20px 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  position: relative;
}
.main-content .icon-row .icon-col .icon-box .btn-flush {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 14px;
  line-height: 100%;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.main-content .icon-row .icon-col .icon-box span {
  display: block;
  font-size: 14px;
  text-transform: capitalize;
  line-height: 100%;
}
.main-content .icon-row .icon-col .icon-box span.icon {
  font-size: 25px;
  color: #fff;
  margin-bottom: 17px;
}
.main-content .icon-row .icon-col .icon-box span.icon-name {
  margin-bottom: -2px;
}
.main-content .icon-row .icon-col .icon-box:hover {
  border-color: rgba(255, 255, 255, 0.2);
  border-style: solid;
}
.main-content .icon-row .icon-col .icon-box:hover .btn-flush {
  opacity: 1;
}
.main-content .team-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
}
.main-content .team-list .avatar {
  width: 35px;
  height: 35px;
}
.main-content .team-list .part-txt {
  width: calc(100% - 50px);
}
.main-content .team-list .part-txt span {
  display: block;
  line-height: 100%;
}
.main-content .team-list .part-txt span.name {
  margin-bottom: 5px;
}
.main-content .team-list .part-txt span.position {
  font-size: 12px;
  opacity: 0.7;
}
.main-content .animation-card {
  overflow: hidden;
}
.main-content .animation-card div:last-child {
  padding: 50px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.main-content .animation-card div:last-child img {
  max-width: 250px;
}
.main-content .error-panel {
  position: fixed;
  top: 25px;
  right: 25px;
  bottom: 25px;
  left: 25px;
  color: #fff;
}
.main-content .error-panel .part-img {
  margin-bottom: 60px;
}
.main-content .error-panel .error-title {
  font-size: 120px;
  font-weight: 600;
  line-height: 1;
  margin-top: -13px;
  margin-bottom: 21px;
}
.main-content .error-panel .error-subtitle {
  font-size: 40px;
  line-height: 1;
  margin-top: -3px;
  margin-bottom: 25px;
}
.main-content .coming-soon-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  color: #fff;
  background: url(../images/coming-soon-bg.jpg) center center no-repeat;
  background-size: cover;
  z-index: 2;
}
.main-content .coming-soon-panel.coming-soon-panel-2 {
  background-image: url(../images/coming-soon-bg-2.jpg);
}
.main-content .coming-soon-panel::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #071739;
  opacity: 0.8;
  z-index: -1;
}
.main-content .coming-soon-panel .part-img {
  margin-bottom: 50px;
}
.main-content .coming-soon-panel .part-txt {
  text-align: center;
}
.main-content .coming-soon-panel .countdown {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 30px;
}
.main-content .coming-soon-panel .countdown li {
  font-size: 50px;
}
.main-content .coming-soon-panel .countdown li span {
  text-align: center;
  display: block;
  line-height: 1;
  min-width: 100px;
  background: #112143;
  padding: 15px;
  border: 1px solid #0D99FF;
  border-radius: 5px;
}
.main-content .coming-soon-panel .countdown li p {
  text-align: center;
  font-size: 16px;
  line-height: 1;
  padding-top: 20px;
  margin-top: -1px;
  margin-bottom: -3px;
}
.main-content .under-construction-panel {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  color: #fff;
  background: url(../images/under-construction-bg.png) center center no-repeat;
  background-size: cover;
  z-index: 2;
}
.main-content .under-construction-panel::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #071739;
  opacity: 0.85;
  z-index: -1;
}
.main-content .under-construction-panel .part-img {
  margin-bottom: 60px;
}
.main-content .under-construction-panel .part-txt {
  text-align: center;
}
.main-content .under-construction-panel .part-txt h2 {
  font-size: 60px;
  font-weight: 600;
  line-height: 1;
  margin-top: -7px;
  margin-bottom: 23px;
}
.main-content .under-construction-panel .part-txt p {
  font-size: 16px;
  line-height: 1;
  margin-top: -3px;
  margin-bottom: 27px;
}
.main-content .pricing-panel {
  overflow: auto;
}
.main-content .pricing-panel .panel-body {
  min-height: 100%;
}
.main-content .pricing-panel .pricing .table-wrap {
  padding-top: 50px;
}
.main-content .pricing-panel .pricing .table {
  min-width: auto;
  margin-bottom: 0;
  border-color: #e4e4e4;
  border-style: solid;
}
.main-content .pricing-panel .pricing .table a {
  color: #fff;
  text-decoration: none;
}
.main-content .pricing-panel .pricing .table .table-secondary {
  border-style: solid;
}
.main-content .pricing-panel .pricing .table thead th {
  border-bottom: 0;
  vertical-align: middle;
}
.main-content .pricing-panel .pricing .table thead th {
  padding: 0;
  width: 17.5%;
}
.main-content .pricing-panel .pricing .table thead th:first-child {
  background: #f7f7f7;
  width: 30%;
  padding: 30px;
}
.main-content .pricing-panel .pricing .table thead th:nth-child(3) {
  border-color: #2d893a;
}
.main-content .pricing-panel .pricing .table thead th:nth-child(4) {
  background: #f2fbf1;
  border-color: #2d893a;
}
.main-content .pricing-panel .pricing .table thead th .table-title h2 {
  font-size: 35px;
  line-height: 45px;
  font-weight: 700;
  color: #495057;
  margin-bottom: 0;
}
.main-content .pricing-panel .pricing .table thead th .table-top {
  text-align: center;
  padding: 30px 20px;
  height: 100%;
}
.main-content .pricing-panel .pricing .table thead th .table-top h3 {
  font-size: 18px;
  line-height: 100%;
  font-weight: 600;
  text-transform: uppercase;
  color: #495057;
  margin-bottom: 20px;
}
.main-content .pricing-panel .pricing .table thead th .table-top h2 {
  font-size: 30px;
  line-height: 100%;
  font-weight: 600;
  color: #3e3e3e;
  margin-bottom: 10px;
}
.main-content .pricing-panel .pricing .table thead th .table-top h2 .type {
  font-size: 14px;
}
.main-content .pricing-panel .pricing .table thead th .table-top p {
  font-size: 16px;
  line-height: 1.3;
  font-weight: 400;
  color: #2e2e2e;
  margin-bottom: 25px;
}
.main-content .pricing-panel .pricing .table thead th:nth-child(4) .table-top {
  position: relative;
}
.main-content .pricing-panel .pricing .table thead th:nth-child(4) .table-top:after {
  content: "Recomended";
  position: absolute;
  bottom: 100%;
  left: -1px;
  right: -1px;
  background: #2d893a;
  border-radius: 10px 10px 0 0;
  text-align: center;
  font-size: 20px;
  font-weight: 400;
  height: 45px;
  line-height: 43px;
  color: #fff;
}
.main-content .pricing-panel .pricing .table tbody tr td {
  color: #797979;
}
.main-content .pricing-panel .pricing .table tbody tr:not(.table-secondary) td:first-child {
  background: #F0F0F0;
  font-size: 16px;
  font-weight: 400;
  color: #797979;
}
.main-content .pricing-panel .pricing .table tbody tr td:first-child {
  font-size: 18px;
  text-align: left;
  font-weight: 600;
  color: #595959;
}
.main-content .pricing-panel .pricing .table tbody tr td:nth-child(3) {
  border-color: #2d893a;
}
.main-content .pricing-panel .pricing .table tbody tr.table-secondary td:nth-child(3) {
  border-color: inherit;
}
.main-content .pricing-panel .pricing .table tbody tr td:nth-child(4) {
  background: #f2fbf1;
  border-color: #2d893a;
  border-style: solid;
}
.main-content .pricing-panel .pricing .table tbody tr.table-secondary td:nth-child(1) {
  border-right-color: #2d893a;
  border-style: solid;
}
.main-content .pricing-panel .pricing .table tbody tr.table-secondary td:nth-child(2) {
  background: #f2fbf1;
  border-color: #2d893a;
  border-style: solid;
}
.main-content .pricing-panel .pricing .table tbody tr td {
  padding: 15px;
  text-align: center;
  vertical-align: middle;
}
.main-content .pricing-panel .pricing .table tbody tr td .icon {
  font-size: 20px;
}
.main-content .pricing-panel .pricing .table tbody tr td .icon.check {
  color: #2c9426;
}
.main-content .pricing-panel .pricing .table tbody tr td .icon.minus {
  color: #b0b0b0;
}
.main-content .pricing-panel-2 {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
}
.main-content .pricing-panel-2 .panel-body {
  min-height: 100%;
}
.main-content .pricing-panel-2 .pricing-table {
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
}
.main-content .pricing-panel-2 .pricing-table .top {
  padding: 30px;
  padding-bottom: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  z-index: 2;
}
.main-content .pricing-panel-2 .pricing-table .top::before {
  content: "";
  position: absolute;
  top: -20px;
  left: -90px;
  height: 100%;
  aspect-ratio: 1/1;
  background: #000;
  border-radius: 20px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  opacity: 0.1;
  z-index: -1;
}
.main-content .pricing-panel-2 .pricing-table .top::after {
  content: "";
  position: absolute;
  top: 10px;
  right: -120px;
  height: 100%;
  aspect-ratio: 1/1;
  background: #000;
  border-radius: 20px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  opacity: 0.1;
  z-index: -1;
}
.main-content .pricing-panel-2 .pricing-table .top p {
  font-size: 16px;
  line-height: 1;
  font-weight: 500;
  text-transform: uppercase;
  color: #fff;
  margin-top: -2px;
  margin-bottom: 17px;
}
.main-content .pricing-panel-2 .pricing-table .top h3 {
  font-size: 40px;
  line-height: 1;
  font-weight: 500;
  color: #fff;
  margin-bottom: 24px;
}
.main-content .pricing-panel-2 .pricing-table .top span {
  display: block;
  -webkit-box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.2);
          box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 5px;
  padding: 5px 20px;
  background: #112143;
  color: #f5f5f5;
  margin-bottom: -15px;
}
.main-content .pricing-panel-2 .pricing-table .middle {
  background: rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 30px;
}
.main-content .pricing-panel-2 .pricing-table .middle ul {
  text-align: center;
  line-height: 3;
  color: #fff;
  margin-top: -3px;
  margin-bottom: -19px;
}
.main-content .pricing-panel-2 .pricing-table .bottom {
  padding: 30px;
  text-align: center;
}

.dropzone {
  min-height: auto;
  padding: 0;
  border: 0;
  background: transparent;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
}
.dropzone.dz-started .dz-message {
  display: block;
  margin-bottom: 5px;
}
.dropzone.dz-component .dz-message .dz-button {
  padding: 57.5px 0;
}
.dropzone .dz-message {
  width: 100%;
  margin: 0;
  color: #c8d4f0;
  border: 1px dashed rgba(255, 255, 255, 0.2);
}
.dropzone .dz-message .dz-button {
  padding: 50px 0;
}
.dropzone .dz-message .dz-button i {
  font-size: 50px;
  margin-top: -3px;
  margin-bottom: 15px;
}
.dropzone .dz-message .dz-button span {
  display: block;
  font-size: 14px;
  line-height: 100%;
  margin-bottom: -2px;
}
.dropzone .dz-preview {
  margin: 0;
  min-height: auto;
  position: relative;
}
.dropzone .dz-preview.dz-image-preview {
  border-radius: 3px;
}
.dropzone .dz-preview.dz-file-preview .dz-image {
  border-radius: 3px;
}
.dropzone .dz-preview .dz-image {
  width: 90px;
  height: 90px;
  border-radius: 3px;
}
.dropzone .dz-preview .dz-progress {
  height: 8px;
  margin-top: -4px;
}
.dropzone .dz-preview .dz-progress .dz-upload {
  background: -webkit-gradient(linear, left top, left bottom, from(#2d893a), to(#1e6529));
  background: linear-gradient(#2d893a, #1e6529);
}
.dropzone .dz-preview .dz-success-mark,
.dropzone .dz-preview .dz-error-mark {
  margin-left: -15px;
  margin-top: -15px;
}
.dropzone .dz-preview .dz-success-mark svg,
.dropzone .dz-preview .dz-error-mark svg {
  width: 30px;
  height: 30px;
}
.dropzone .dz-preview .dz-success-mark svg g > * {
  fill: #2d893a;
}
.dropzone .dz-preview .dz-error-mark svg g {
  fill: #f00000;
}
.dropzone .dz-preview .dz-error-message {
  top: calc(100% + 6px);
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
}
.dropzone .dz-preview .dz-remove {
  font-size: 0;
  width: 15px;
  height: 15px;
  position: absolute;
  top: -5px;
  right: -5px;
  z-index: 21;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.dropzone .dz-preview .dz-remove::after {
  content: "\f00d";
  font-family: "Font Awesome 6 Pro";
  font-weight: 500;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  line-height: 15px;
  background: #f00000;
  color: #f7faff;
  border-radius: 50%;
  font-size: 12px;
}
.dropzone .dz-preview .dz-details {
  padding: 10px;
  font-family: "Lato", sans-serif;
  font-size: 12px;
  min-height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.dropzone .dz-preview .dz-details .dz-size {
  font-size: 14px;
  font-weight: 700;
}
.dropzone .dz-preview:hover .dz-remove {
  opacity: 1;
}

.modal-body.file-details-modal {
  padding: 25px;
}

.file-details {
  padding-bottom: 10px;
}
.file-details li {
  font-family: "Lato", sans-serif;
  line-height: 100%;
  color: #c8d4f0;
  margin-top: -3px;
  margin-bottom: 18px;
}
.file-details li span {
  font-weight: 500;
  color: #c4c4c4;
  margin-right: 10px;
}

.voice-call {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 15px 0;
  position: relative;
  z-index: 2;
}
.voice-call::after {
  content: "";
  position: absolute;
  bottom: -16px;
  left: -16px;
  right: -16px;
  height: 50px;
  border-radius: 0 0 3px 3px;
  z-index: -1;
}
.voice-call .user {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 30px;
}
.voice-call .user .avatar {
  margin-bottom: 20px;
}
.voice-call .user .user-name {
  display: block;
  font-size: 20px;
  line-height: 100%;
  font-weight: 500;
  color: #c8d4f0;
  margin-top: -2px;
  margin-bottom: 16px;
}
.voice-call .user .call-status {
  font-size: 14px;
  line-height: 100%;
  color: #c8d4f0;
  margin-bottom: -2px;
}
.voice-call .call-option {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 10px;
  background: #071739;
  border-radius: 25px;
  padding: 10px 15px;
  margin: auto;
}

.video-call .user {
  position: relative;
  border-radius: 3px;
  overflow: hidden;
}
.video-call .user .user-preview {
  height: 400px;
}
.video-call .user .part-txt {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(#000));
  background: linear-gradient(transparent, #000);
  padding: 20px;
}
.video-call .user .part-txt .user-name {
  display: block;
  font-size: 20px;
  line-height: 100%;
  font-weight: 500;
  color: #fff;
  margin-top: -2px;
  margin-bottom: 6px;
}
.video-call .user .part-txt .call-status {
  font-size: 14px;
  line-height: 100%;
  color: #fff;
  opacity: 0.7;
  margin-bottom: -2px;
}
.video-call .bottom {
  height: 66px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  padding-top: 15px;
}
.video-call .bottom .call-option {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  gap: 10px;
  background: #071739;
  border-radius: 25px;
  padding: 10px 15px;
}
.video-call .bottom .admin-preview {
  width: 140px;
  height: 120px;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 20px;
  position: relative;
}

.footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 45px;
  background: #112143;
  text-align: center;
  z-index: 12;
}
.footer p {
  font-size: 14px;
  line-height: 45px;
  color: #A9B4CC;
  margin-bottom: 0;
}

.modal .ck ul,
.main-content .ck ul,
.compose-mail .ck ul,
.mail-details .ck ul {
  padding-left: 2rem;
  list-style: disc;
}
.modal .ck-editor,
.main-content .ck-editor,
.compose-mail .ck-editor,
.mail-details .ck-editor {
  border-radius: 3px;
}
.modal .ck-content,
.main-content .ck-content,
.compose-mail .ck-content,
.mail-details .ck-content {
  height: 300px;
}
.modal .ck.ck-editor__main > .ck-editor__editable,
.main-content .ck.ck-editor__main > .ck-editor__editable,
.compose-mail .ck.ck-editor__main > .ck-editor__editable,
.mail-details .ck.ck-editor__main > .ck-editor__editable {
  background: #071739;
  color: #97a3bb;
}
.modal .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph,
.main-content .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph,
.compose-mail .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph,
.mail-details .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph {
  color: #97a3bb;
}
.modal .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused),
.main-content .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused),
.compose-mail .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused),
.mail-details .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
  border-color: rgba(255, 255, 255, 0.15);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.modal .ck.ck-toolbar,
.main-content .ck.ck-toolbar,
.compose-mail .ck.ck-toolbar,
.mail-details .ck.ck-toolbar {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.15);
}
.modal .ck.ck-input,
.main-content .ck.ck-input,
.compose-mail .ck.ck-input,
.mail-details .ck.ck-input {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.15);
  color: #c8d4f0;
}
.modal .ck.ck-input:focus,
.main-content .ck.ck-input:focus,
.compose-mail .ck.ck-input:focus,
.mail-details .ck.ck-input:focus {
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
}
.modal .ck-labeled-field-view__status,
.main-content .ck-labeled-field-view__status,
.compose-mail .ck-labeled-field-view__status,
.mail-details .ck-labeled-field-view__status {
  color: #8b97b2 !important;
}
.modal .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
.main-content .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
.compose-mail .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
.mail-details .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {
  background: #112143;
  color: #8b97b2;
}
.modal .ck-insert-table-dropdown__label, .modal .ck.ck-button .ck-button__label, .modal a.ck.ck-button .ck-button__label,
.main-content .ck-insert-table-dropdown__label,
.main-content .ck.ck-button .ck-button__label,
.main-content a.ck.ck-button .ck-button__label,
.compose-mail .ck-insert-table-dropdown__label,
.compose-mail .ck.ck-button .ck-button__label,
.compose-mail a.ck.ck-button .ck-button__label,
.mail-details .ck-insert-table-dropdown__label,
.mail-details .ck.ck-button .ck-button__label,
.mail-details a.ck.ck-button .ck-button__label {
  color: #F9F9F9 !important;
}
.modal .ck.ck-toolbar .ck.ck-toolbar__separator,
.main-content .ck.ck-toolbar .ck.ck-toolbar__separator,
.compose-mail .ck.ck-toolbar .ck.ck-toolbar__separator,
.mail-details .ck.ck-toolbar .ck.ck-toolbar__separator {
  background: rgba(255, 255, 255, 0.15);
}
.modal .ck.ck-button.ck-on:not(.ck-disabled):hover, .modal a.ck.ck-button.ck-on:not(.ck-disabled):hover,
.modal .ck.ck-button.ck-on, .modal a.ck.ck-button.ck-on,
.modal .ck.ck-button:not(.ck-disabled):hover, .modal a.ck.ck-button:not(.ck-disabled):hover,
.main-content .ck.ck-button.ck-on:not(.ck-disabled):hover,
.main-content a.ck.ck-button.ck-on:not(.ck-disabled):hover,
.main-content .ck.ck-button.ck-on,
.main-content a.ck.ck-button.ck-on,
.main-content .ck.ck-button:not(.ck-disabled):hover,
.main-content a.ck.ck-button:not(.ck-disabled):hover,
.compose-mail .ck.ck-button.ck-on:not(.ck-disabled):hover,
.compose-mail a.ck.ck-button.ck-on:not(.ck-disabled):hover,
.compose-mail .ck.ck-button.ck-on,
.compose-mail a.ck.ck-button.ck-on,
.compose-mail .ck.ck-button:not(.ck-disabled):hover,
.compose-mail a.ck.ck-button:not(.ck-disabled):hover,
.mail-details .ck.ck-button.ck-on:not(.ck-disabled):hover,
.mail-details a.ck.ck-button.ck-on:not(.ck-disabled):hover,
.mail-details .ck.ck-button.ck-on,
.mail-details a.ck.ck-button.ck-on,
.mail-details .ck.ck-button:not(.ck-disabled):hover,
.mail-details a.ck.ck-button:not(.ck-disabled):hover {
  background: rgba(255, 255, 255, 0.1);
}
.modal .ck.ck-dropdown__panel,
.main-content .ck.ck-dropdown__panel,
.compose-mail .ck.ck-dropdown__panel,
.mail-details .ck.ck-dropdown__panel {
  background: #112143;
  border-color: rgba(255, 255, 255, 0.1);
}
.modal .ck.ck-dropdown__panel .ck.ck-list,
.main-content .ck.ck-dropdown__panel .ck.ck-list,
.compose-mail .ck.ck-dropdown__panel .ck.ck-list,
.mail-details .ck.ck-dropdown__panel .ck.ck-list {
  background: transparent;
  padding: 0;
}
.modal .ck.ck-dropdown__panel .ck.ck-list button,
.main-content .ck.ck-dropdown__panel .ck.ck-list button,
.compose-mail .ck.ck-dropdown__panel .ck.ck-list button,
.mail-details .ck.ck-dropdown__panel .ck.ck-list button {
  color: #c8d4f0 !important;
}
.modal .ck.ck-dropdown__panel .ck.ck-list button span,
.main-content .ck.ck-dropdown__panel .ck.ck-list button span,
.compose-mail .ck.ck-dropdown__panel .ck.ck-list button span,
.mail-details .ck.ck-dropdown__panel .ck.ck-list button span {
  color: #c8d4f0 !important;
}
.modal .ck-button,
.main-content .ck-button,
.compose-mail .ck-button,
.mail-details .ck-button {
  -webkit-box-shadow: 0 0 !important;
          box-shadow: 0 0 !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}
.modal .ck.ck-list__item .ck-button.ck-on,
.main-content .ck.ck-list__item .ck-button.ck-on,
.compose-mail .ck.ck-list__item .ck-button.ck-on,
.mail-details .ck.ck-list__item .ck-button.ck-on {
  background: #071739;
}
.modal .ck.ck-list__item .ck-button:hover:not(.ck-disabled),
.main-content .ck.ck-list__item .ck-button:hover:not(.ck-disabled),
.compose-mail .ck.ck-list__item .ck-button:hover:not(.ck-disabled),
.mail-details .ck.ck-list__item .ck-button:hover:not(.ck-disabled) {
  background: rgba(255, 255, 255, 0.1);
}
.modal .ck.ck-list__item .ck-button.ck-on:hover:not(.ck-disabled),
.main-content .ck.ck-list__item .ck-button.ck-on:hover:not(.ck-disabled),
.compose-mail .ck.ck-list__item .ck-button.ck-on:hover:not(.ck-disabled),
.mail-details .ck.ck-list__item .ck-button.ck-on:hover:not(.ck-disabled) {
  background: #071739;
}

.ck.ck-button {
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
}
.ck.ck-button svg circle,
.ck.ck-button svg path {
  fill: #F9F9F9 !important;
}

.ck.ck-button:active,
.ck.ck-button:focus,
a.ck.ck-button:active,
a.ck.ck-button:focus {
  -webkit-box-shadow: 0 0 !important;
          box-shadow: 0 0 !important;
}

.ck.ck-link-actions .ck-button.ck-link-actions__preview .ck-button__label {
  color: #c8d4f0 !important;
}

.ck.ck-balloon-panel {
  background: #112143 !important;
  border: 0 !important;
  z-index: 9999 !important;
}
.ck.ck-balloon-panel[class*=arrow_n]::before, .ck.ck-balloon-panel[class*=arrow_n]::after {
  border-color: transparent transparent #112143 transparent !important;
}
.ck.ck-balloon-panel input {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.15);
  color: #c8d4f0;
}
.ck.ck-balloon-panel input:focus {
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
}
.ck.ck-balloon-panel label {
  background: #112143 !important;
  color: #c8d4f0;
}
.ck.ck-balloon-panel button {
  -webkit-box-shadow: 0 0 !important;
          box-shadow: 0 0 !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}
.ck.ck-balloon-panel button:hover:not(.ck-disabled) {
  background: rgba(255, 255, 255, 0.1);
}
.ck.ck-balloon-panel .ck.ck-toolbar {
  background: #112143;
}
.ck.ck-balloon-panel .ck.ck-toolbar .ck-button:focus, .ck.ck-balloon-panel .ck.ck-toolbar .ck-button:active, .ck.ck-balloon-panel .ck.ck-toolbar .ck-button.active, .ck.ck-balloon-panel .ck.ck-toolbar .ck-button.ck-on {
  background: rgba(255, 255, 255, 0.1) !important;
}

.compose-mail {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  z-index: 9999;
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  opacity: 0;
  visibility: hidden;
}
.compose-mail.expanded .panel {
  width: 80%;
}
.compose-mail.expanded .ck-content {
  height: 450px;
}
.compose-mail.minimized {
  top: auto;
  bottom: 0;
  left: auto;
  right: 30px;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  height: -webkit-max-content;
  height: -moz-max-content;
  height: max-content;
}
.compose-mail.minimized .panel {
  width: 250px;
}
.compose-mail.minimized .panel-header {
  border-bottom: 0;
  padding: 0 7px;
  height: 45px;
}
.compose-mail.minimized .panel-header h5 {
  font-size: 16px;
  line-height: 45px;
  cursor: pointer;
}
.compose-mail.minimized .panel-body {
  display: none;
}
.compose-mail.open {
  opacity: 1;
  visibility: visible;
}
.compose-mail.open .panel {
  -webkit-transform: translate(0);
          transform: translate(0);
  -webkit-transition: 0s;
  transition: 0s;
}
.compose-mail .panel {
  width: 700px;
  -webkit-box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.3);
          box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.3);
  -webkit-transform: translateY(-25%);
          transform: translateY(-25%);
  -webkit-transition: all 0.3s ease-in;
  transition: all 0.3s ease-in;
}
.compose-mail .ck.ck-editor {
  margin-bottom: 15px;
}

.mail-details {
  width: 45%;
  border: 1px dashed rgba(255, 255, 255, 0.1);
  margin-left: -1px;
  padding: 20px;
  position: relative;
  color: #c8d4f0;
}
.mail-details .mail-conversation {
  height: calc(100% - 93px);
}
.mail-details .mail-conversation.reply-panel-opened {
  height: calc(100% - 293px);
}
.mail-details .single-mail {
  border-bottom: 1px dashed rgba(255, 255, 255, 0.08);
  padding-top: 10px;
  padding-bottom: 10px;
}
.mail-details .single-mail:first-child {
  padding-top: 0;
}
.mail-details .single-mail:last-child {
  border-bottom: 0;
}
.mail-details .single-mail:last-child .mail-body {
  display: block;
}
.mail-details .single-mail .mail-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  cursor: pointer;
}
.mail-details .single-mail .mail-top .mail-profile {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 10px;
}
.mail-details .single-mail .mail-top .mail-profile .part-txt {
  margin-top: -2px;
}
.mail-details .single-mail .mail-top .mail-profile .part-txt span {
  display: block;
  font-size: 14px;
  line-height: 100%;
}
.mail-details .single-mail .mail-top .mail-profile .part-txt span.name {
  color: #fff;
  margin-bottom: 7px;
}
.mail-details .single-mail .mail-top .mail-profile .part-txt span.mail {
  font-size: 12px;
  color: #c8d4f0;
  margin-bottom: -2px;
}
.mail-details .single-mail .mail-top .mail-action {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  gap: 15px;
}
.mail-details .single-mail .mail-top .mail-action .date {
  display: inline-block;
  font-size: 12px;
  line-height: 100%;
}
.mail-details .single-mail .mail-top .mail-action button {
  font-size: 12px;
  line-height: 100%;
  margin: 0;
}
.mail-details .single-mail .mail-body {
  padding-top: 20px;
  padding-bottom: 10px;
  display: none;
}
.mail-details .single-mail .mail-body > *:last-child {
  margin-bottom: -5px;
}
.mail-details .single-mail .mail-body p {
  font-family: "Lato", sans-serif;
  font-size: 14px;
  line-height: 1.4;
  margin-top: -5px;
  margin-bottom: 15px;
}
.mail-details .mail-reply-option {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: #112143;
  border-top: 1px dashed rgba(255, 255, 255, 0.1);
}
.mail-details .mail-reply-option .ck-content {
  height: 100px;
  margin-bottom: 15px;
}
.mail-details .mail-reply-option .btn-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.mail-details-expanded {
  position: fixed;
  top: 50px;
  bottom: 50px;
  left: 50%;
  width: 80%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background: #112143;
  border-radius: 3px;
  opacity: 0;
  visibility: hidden;
  z-index: 1055;
}
.mail-details-expanded.active {
  opacity: 1;
  visibility: visible;
}
.mail-details-expanded .mail-details {
  width: 100%;
  height: 100%;
  border-style: solid;
  border-radius: 3px;
  border-color: rgba(255, 255, 255, 0.3);
}
.mail-details-expanded .mail-details .mail-conversation.reply-panel-opened {
  height: calc(100% - 443px);
}
.mail-details-expanded .mail-details .mail-reply-option .ck-content {
  height: 250px;
}

.view-task h6 {
  font-size: 16px;
  line-height: 100%;
  color: #fff;
  margin-top: -1px;
  margin-bottom: 12px;
}
.view-task .task-created {
  display: block;
  font-size: 14px;
  line-height: 100%;
  margin-bottom: 20px;
}
.view-task .task-created a {
  color: #fff;
  text-decoration: underline;
}
.view-task .task-dscr {
  margin-bottom: 20px;
}
.view-task .task-dscr p {
  font-size: 14px;
  line-height: 1.4;
  margin-top: -3px;
  margin-bottom: 9px;
}
.view-task .task-dscr .task-attachment {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 10px;
}
.view-task .task-dscr .task-attachment .attched-box {
  width: 180px;
  padding: 5px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}
.view-task .task-dscr .task-attachment .attched-box img {
  border-radius: 2px;
}
.view-task .comments .add-a-comment {
  margin-bottom: 15px;
}
.view-task .comments .single-comment {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  position: relative;
}
.view-task .comments .single-comment:last-child {
  margin-bottom: 0;
}
.view-task .comments .single-comment .comment-delete {
  position: absolute;
  top: 50px;
  left: 20px;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  font-size: 14px;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
.view-task .comments .single-comment .part-txt {
  width: calc(100% - 50px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 3px;
  padding: 10px;
}
.view-task .comments .single-comment .part-txt p {
  font-size: 14px;
  line-height: 1.4;
  margin-top: -3px;
  margin-bottom: -6px;
}
.view-task .comments .single-comment:hover .comment-delete {
  opacity: 1;
}
.view-task .task-info-box {
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 3px;
  padding: 15px;
  margin-bottom: 15px;
}
.view-task .task-info-box:last-child {
  margin-bottom: 0;
}
.view-task .task-info-box h6 {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  padding-bottom: 7px;
  margin-bottom: 14px;
}
.view-task .task-info-box ul {
  margin-top: -1px;
  margin-bottom: -2px;
}
.view-task .task-info-box ul li {
  font-size: 14px;
  line-height: 100%;
  margin-bottom: 10px;
}
.view-task .task-info-box ul li:last-child {
  margin-bottom: 0;
}
.view-task .task-info-box ul li .part-icon {
  display: inline-block;
  width: 15px;
}

.icon-alert {
  position: fixed;
  top: 30px;
  left: 50%;
  width: 30%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  z-index: 1055;
}
.icon-alert.show {
  top: 70px;
  opacity: 1;
  visibility: visible;
}

/* ===== Main Content For Dark Theme ===== */
.dark-theme .main-content::after {
  background-image: url(../images/bg-dark.jpg);
  background-size: cover;
  opacity: 0.05;
}
.dark-theme .main-content.login-panel::after {
  background: transparent;
}
.dark-theme .main-content .dashboard-breadcrumb {
  background: #242526;
}
.dark-theme .main-content .dashboard-filter {
  background: #242526;
}
.dark-theme .main-content .dashboard-filter .form-control {
  color: #A9B4CC;
}
.dark-theme .main-content .dashboard-top-box {
  color: #e4e4e4 !important;
}
.dark-theme .main-content .dashboard-top-box .left a {
  color: #A9B4CC;
}
.dark-theme .main-content .dashboard-top-box .right .part-icon {
  background: rgba(255, 255, 255, 0.05);
}
.dark-theme .main-content .dashboard-top-box:hover .right .part-icon.text-light {
  color: #fff !important;
}
.dark-theme .main-content .visitor-table {
  color: #A9B4CC;
}
.dark-theme .main-content .visitor-table tbody tr:hover {
  background: rgba(255, 255, 255, 0.03);
}
.dark-theme .main-content .visitor-table tbody tr:hover td {
  color: #e4e4e4;
}
.dark-theme .main-content .new-customer-table {
  color: #c4c4c4;
}
.dark-theme .main-content .new-customer-table tr:hover td {
  color: #e4e4e4;
}
.dark-theme .main-content .recent-order-table {
  color: #A9B4CC;
}
.dark-theme .main-content .recent-order-table thead {
  background: #3A3B3C;
  color: #e4e4e4;
}
.dark-theme .main-content .recent-order-table tr.selected {
  background: rgba(255, 255, 255, 0.03);
}
.dark-theme .main-content .recent-order-table .btn-box button {
  color: #A9B4CC;
}
.dark-theme .main-content .recent-order-table .btn-box button:hover, .dark-theme .main-content .recent-order-table .btn-box button:focus {
  color: #fff;
}
.dark-theme .main-content .table-bottom-control {
  color: #A9B4CC;
}
.dark-theme .main-content .table-bottom-control .btn {
  background: rgba(255, 255, 255, 0.07);
}
.dark-theme .main-content .table-bottom-control .previous,
.dark-theme .main-content .table-bottom-control .next {
  background: transparent;
}
.dark-theme .main-content .table-filter-option .dataTables_length label {
  color: #A9B4CC;
}
.dark-theme .main-content .digi-dataTable {
  color: #A9B4CC;
}
.dark-theme .main-content .digi-dataTable thead {
  background: #3A3B3C;
  color: #e4e4e4;
}
.dark-theme .main-content .digi-dataTable tbody tr.selected {
  background: #18191A;
}
.dark-theme .main-content .digi-dataTable tr.odd > * {
  color: #A9B4CC;
}
.dark-theme .main-content .digi-dataTable .btn-box button:not(.btn) {
  color: #e4e4e4;
}
.dark-theme .main-content .digi-dataTable .btn-box button:not(.btn):hover {
  color: #fff;
}
.dark-theme .main-content .table a {
  color: #A9B4CC;
}
.dark-theme .main-content .recent-project-table {
  color: #e4e4e4;
}
.dark-theme .main-content .recent-project-table td {
  color: #A9B4CC !important;
}
.dark-theme .main-content .activity-box {
  color: #A9B4CC;
}
.dark-theme .main-content .invoice-table thead {
  background: #3A3B3C;
}
.dark-theme .main-content .task-table .form-check label {
  color: #A9B4CC;
}
.dark-theme .main-content .notice-board-table thead,
.dark-theme .main-content .deadline-table thead {
  background: #3A3B3C;
}
.dark-theme .main-content .hr-recent-activity {
  color: #A9B4CC;
}
.dark-theme .main-content .hr-recent-activity li .activity-name {
  color: #c4c4c4;
}
.dark-theme .main-content .upcoming-interview li .part-txt span {
  color: #c4c4c4;
}
.dark-theme .main-content .product-table-quantity ul {
  color: #A9B4CC;
}
.dark-theme .main-content .product-table-quantity ul .text-white {
  color: #e4e4e4 !important;
}
.dark-theme .main-content .perma-txt {
  color: #A9B4CC;
}
.dark-theme .main-content .perma-txt .btn-flush {
  color: #fff;
}
.dark-theme .main-content .product-image-upload .part-txt h5 span {
  color: #A9B4CC;
}
.dark-theme .main-content .jquery-uploader-preview-container .jquery-uploader-select-card .upload-button,
.dark-theme .main-content .jquery-uploader-preview-container .jquery-uploader-card .upload-button {
  color: #A9B4CC;
}
.dark-theme .main-content .product-data-tab .input-additional-txt,
.dark-theme .main-content .product-data-tab label {
  color: #A9B4CC;
}
.dark-theme .main-content .publish-date {
  color: #e4e4e4;
}
.dark-theme .main-content .publish-date input {
  color: #A9B4CC;
}
.dark-theme .main-content .product-tag-area .input-txt {
  color: #A9B4CC;
}
.dark-theme .main-content .product-tag-area .all-tags .item {
  color: #e4e4e4;
}
.dark-theme .main-content .table-product-card .part-txt span {
  color: #A9B4CC;
}
.dark-theme .main-content .table-product-card .part-txt span.product-name {
  color: #e4e4e4;
}
.dark-theme .main-content .chatting-panel .active-members .owl-nav {
  color: #A9B4CC;
}
.dark-theme .main-content .chatting-panel .active-members .owl-nav button {
  background: -webkit-gradient(linear, left top, right top, color-stop(30%, #242526), to(transparent));
  background: linear-gradient(90deg, #242526 30%, transparent);
}
.dark-theme .main-content .chatting-panel .active-members .owl-nav button.owl-next {
  background: -webkit-gradient(linear, right top, left top, color-stop(30%, #242526), to(transparent));
  background: linear-gradient(-90deg, #242526 30%, transparent);
}
.dark-theme .main-content .chatting-panel .active-members .owl-nav button:hover {
  color: #fff;
}
.dark-theme .main-content .chatting-panel .message-list .single-message {
  color: #A9B4CC;
}
.dark-theme .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt {
  color: #e4e4e4;
}
.dark-theme .main-content .chatting-panel .chatting-area .chat-top-bar .search-in-chat {
  background: #242526;
}
.dark-theme .main-content .chatting-panel .msg-area {
  background: #18191A;
}
.dark-theme .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner {
  background: #242526;
}
.dark-theme .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner:last-child::after {
  border-bottom-color: #242526;
}
.dark-theme .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner p {
  color: #A9B4CC;
}
.dark-theme .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner .sent-status {
  color: #e4e4e4;
}
.dark-theme .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option button {
  color: #fff;
  opacity: 1;
}
.dark-theme .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option .msg-time {
  color: #fff;
  opacity: 1;
}
.dark-theme .main-content .chatting-panel .msg-area .day-divider span {
  color: #e4e4e4;
}
.dark-theme .main-content .chatting-panel .msg-area .day-divider span::before, .dark-theme .main-content .chatting-panel .msg-area .day-divider span::after {
  border-color: rgba(255, 255, 255, 0.3);
}
.dark-theme .main-content .chatting-panel .user-short .part-txt {
  color: #e4e4e4;
}
.dark-theme .main-content .chatting-panel .chatting-option ul li a {
  color: #A9B4CC;
}
.dark-theme .main-content .chatting-panel .chatting-option ul li a:hover {
  color: #fff;
}
.dark-theme .main-content .email-panel .emial-menu-list ul li button:not(.active) .part-txt {
  color: #A9B4CC;
}
.dark-theme .main-content .email-panel .emial-menu-list ul li button:hover .part-txt {
  color: #fff;
}
.dark-theme .main-content .email-panel .table-bottom-control {
  background: #18191A;
}
.dark-theme .main-content .invoice {
  color: #A9B4CC;
}
.dark-theme .main-content .invoice .table {
  color: #c4c4c4;
}
.dark-theme .main-content .invoice .total-payment-area ul li:last-child {
  color: #fff;
}
.dark-theme .main-content .login-body.login-body-2 .top {
  background: #3A3B3C;
}
.dark-theme .main-content .login-body .bottom .panel-title {
  color: #e4e4e4;
}
.dark-theme .main-content .login-body .bottom .form-control {
  background: #313131;
  color: #e4e4e4;
  border-color: rgba(255, 255, 255, 0.1);
}
.dark-theme .main-content .login-body .bottom .form-control::-webkit-input-placeholder {
  opacity: 0.8;
}
.dark-theme .main-content .login-body .bottom .form-control::-moz-placeholder {
  opacity: 0.8;
}
.dark-theme .main-content .login-body .bottom .form-control:-ms-input-placeholder {
  opacity: 0.8;
}
.dark-theme .main-content .login-body .bottom .form-control::-ms-input-placeholder {
  opacity: 0.8;
}
.dark-theme .main-content .login-body .bottom .form-control::placeholder {
  opacity: 0.8;
}
.dark-theme .main-content .login-body .bottom .input-group-text {
  background: #313131;
  border: 0;
}
.dark-theme .main-content .login-body .bottom .other-option p {
  color: #A9B4CC;
}
.dark-theme .main-content .login-body .bottom .other-option .social-box a {
  background: #f5f5f5;
}
.dark-theme .main-content .login-body .bottom .password-show {
  color: #e4e4e4;
}
.dark-theme .main-content .login-body .text-white {
  color: #c4c4c4 !important;
}
.dark-theme .main-content .profile-sidebar-title {
  color: #e4e4e4;
}
.dark-theme .main-content .profile-sidebar-subtitle {
  color: #c4c4c4;
}
.dark-theme .main-content .profile-sidebar .top .part-txt h4 {
  color: #c4c4c4;
}
.dark-theme .main-content .profile-sidebar .top .part-txt span {
  color: #A9B4CC;
}
.dark-theme .main-content .profile-sidebar .top .part-txt .admin-social a {
  color: #e4e4e4;
}
.dark-theme .main-content .profile-sidebar .bottom ul li {
  color: #A9B4CC;
}
.dark-theme .main-content .profile-sidebar .bottom ul li span {
  color: #c4c4c4;
}
.dark-theme .main-content .profile-sidebar .bottom p {
  color: #c4c4c4;
}
.dark-theme .main-content .user-activity ul li {
  color: #c4c4c4;
}
.dark-theme .main-content .user-activity ul li::after {
  border-color: rgba(255, 255, 255, 0.1);
}
.dark-theme .main-content .user-activity ul li .left,
.dark-theme .main-content .user-activity ul li .right {
  background: #242526;
}
.dark-theme .main-content .user-activity-details,
.dark-theme .main-content .user-activity-date {
  color: #A9B4CC;
}
.dark-theme .main-content .profile-edit-tab-title h6 {
  color: #e4e4e4;
  background: #242526;
}
.dark-theme .main-content .profile-edit-tab .admin-profile .admin-name {
  color: #c4c4c4;
}
.dark-theme .main-content .profile-edit-tab .admin-profile .admin-role {
  color: #A9B4CC;
}
.dark-theme .main-content .file-manager-sidebar {
  color: #A9B4CC;
}
.dark-theme .main-content .file-manager-sidebar nav button {
  color: #c4c4c4;
}
.dark-theme .main-content .file-manager-sidebar nav button.active {
  color: #fff;
}
.dark-theme .main-content .file-manager-sidebar .connected-app .file-manager-sidebar-title {
  color: #e4e4e4;
}
.dark-theme .main-content .file-manager-sidebar .connected-app li a {
  color: #A9B4CC;
}
.dark-theme .main-content .file-manager-card {
  background: #3A3B3C;
  color: #A9B4CC;
}
.dark-theme .main-content .file-manager-card .top .action {
  color: #c4c4c4;
}
.dark-theme .main-content .file-manager-card .bottom .left .folder-name {
  color: #e4e4e4;
}
.dark-theme .main-content .file-card {
  background: #3A3B3C;
}
.dark-theme .main-content .file-card .part-txt {
  color: #A9B4CC;
}
.dark-theme .main-content .file-card .part-txt .file-name {
  color: #e4e4e4;
}
.dark-theme .main-content .file-manager-row .file-manager-col-head {
  background: #595959;
}
.dark-theme .main-content .file-manager-row .file-manager-col-head span {
  color: #e4e4e4;
}
.dark-theme .main-content .file-manager-row.list-view .file-manager-col .file-card .file-type {
  color: #A9B4CC;
}
.dark-theme .main-content .part-btn p {
  color: #A9B4CC;
}
.dark-theme .main-content .file-manager-settings-subtitle {
  color: #e4e4e4;
}
.dark-theme .main-content .plan-name {
  color: #c4c4c4;
}
.dark-theme .main-content .used-storage {
  color: #A9B4CC;
}
.dark-theme .main-content .file-manager-settings-table {
  color: #A9B4CC;
}
.dark-theme .footer {
  background: #242526;
}
.dark-theme .footer p {
  color: #A9B4CC;
}
.dark-theme .modal .ck.ck-editor__main > .ck-editor__editable,
.dark-theme .main-content .ck.ck-editor__main > .ck-editor__editable,
.dark-theme .compose-mail .ck.ck-editor__main > .ck-editor__editable,
.dark-theme .mail-details .ck.ck-editor__main > .ck-editor__editable {
  background: #18191A;
  color: #c4c4c4;
}
.dark-theme .modal .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph,
.dark-theme .main-content .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph,
.dark-theme .compose-mail .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph,
.dark-theme .mail-details .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph {
  color: #c4c4c4;
}
.dark-theme .modal .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
.dark-theme .main-content .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
.dark-theme .compose-mail .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
.dark-theme .mail-details .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {
  background: #242526;
  color: #A9B4CC;
}
.dark-theme .modal .ck.ck-dropdown__panel,
.dark-theme .main-content .ck.ck-dropdown__panel,
.dark-theme .compose-mail .ck.ck-dropdown__panel,
.dark-theme .mail-details .ck.ck-dropdown__panel {
  background: #242526;
}
.dark-theme .modal .ck.ck-dropdown__panel .ck.ck-list button,
.dark-theme .main-content .ck.ck-dropdown__panel .ck.ck-list button,
.dark-theme .compose-mail .ck.ck-dropdown__panel .ck.ck-list button,
.dark-theme .mail-details .ck.ck-dropdown__panel .ck.ck-list button {
  color: #A9B4CC !important;
}
.dark-theme .modal .ck.ck-dropdown__panel .ck.ck-list button.ck-on,
.dark-theme .main-content .ck.ck-dropdown__panel .ck.ck-list button.ck-on,
.dark-theme .compose-mail .ck.ck-dropdown__panel .ck.ck-list button.ck-on,
.dark-theme .mail-details .ck.ck-dropdown__panel .ck.ck-list button.ck-on {
  background: #18191A;
}
.dark-theme .modal .ck.ck-dropdown__panel .ck.ck-list button span,
.dark-theme .main-content .ck.ck-dropdown__panel .ck.ck-list button span,
.dark-theme .compose-mail .ck.ck-dropdown__panel .ck.ck-list button span,
.dark-theme .mail-details .ck.ck-dropdown__panel .ck.ck-list button span {
  color: #A9B4CC !important;
}
.dark-theme .modal .ck-labeled-field-view__status,
.dark-theme .main-content .ck-labeled-field-view__status,
.dark-theme .compose-mail .ck-labeled-field-view__status,
.dark-theme .mail-details .ck-labeled-field-view__status {
  color: #A9B4CC !important;
}
.dark-theme .ck.ck-balloon-panel {
  background: #242526 !important;
  z-index: 9999 !important;
}
.dark-theme .ck.ck-balloon-panel[class*=arrow_n]::before, .dark-theme .ck.ck-balloon-panel[class*=arrow_n]::after {
  border-color: transparent transparent #242526 transparent !important;
}
.dark-theme .ck.ck-balloon-panel[class*=arrow_s]::before, .dark-theme .ck.ck-balloon-panel[class*=arrow_s]::after {
  border-color: #242526 transparent transparent transparent !important;
}
.dark-theme .ck.ck-balloon-panel input {
  border-color: rgba(255, 255, 255, 0.15);
  color: #A9B4CC;
}
.dark-theme .ck.ck-balloon-panel label {
  background: #242526 !important;
  color: #A9B4CC;
}
.dark-theme .ck.ck-balloon-panel button {
  border-color: rgba(255, 255, 255, 0.1) !important;
}
.dark-theme .ck.ck-balloon-panel button:hover:not(.ck-disabled) {
  background: rgba(255, 255, 255, 0.1);
}
.dark-theme .ck.ck-balloon-panel .ck.ck-toolbar {
  background: #242526;
}
.dark-theme .ck.ck-balloon-panel .ck.ck-toolbar .ck-button:focus, .dark-theme .ck.ck-balloon-panel .ck.ck-toolbar .ck-button:active, .dark-theme .ck.ck-balloon-panel .ck.ck-toolbar .ck-button.active, .dark-theme .ck.ck-balloon-panel .ck.ck-toolbar .ck-button.ck-on {
  background: rgba(255, 255, 255, 0.1) !important;
}
.dark-theme .voice-call .user .user-name {
  color: #e4e4e4;
}
.dark-theme .voice-call .user .call-status {
  color: #A9B4CC;
}
.dark-theme .voice-call .call-option {
  background: #3A3B3C;
}
.dark-theme .video-call .bottom .call-option {
  background: #3A3B3C;
}
.dark-theme .mail-details-expanded {
  background: #18191A;
}
.dark-theme .mail-details {
  background: #18191A;
  border-color: rgba(255, 255, 255, 0.15);
  color: #A9B4CC;
}
.dark-theme .mail-details .single-mail {
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .mail-details .single-mail .mail-top .mail-profile .part-txt span.name {
  color: #c4c4c4;
}
.dark-theme .mail-details .single-mail .mail-top .mail-profile .part-txt span.mail {
  color: #A9B4CC;
}
.dark-theme .mail-details .mail-reply-option {
  background: #18191A;
  border-color: rgba(255, 255, 255, 0.15);
}
.dark-theme .file-details li {
  color: #A9B4CC;
}
.dark-theme .file-details li span {
  color: #e4e4e4;
}

/* ===== Main Content For Light Theme ===== */
.light-theme .main-content {
  background-color: hsla(0, 0%, 85.04%, 0.4);
}
.light-theme .main-content::after {
  background-image: url(../images/bg-white.jpg);
  background-size: cover;
  opacity: 0.08;
}
.light-theme .main-content.login-panel {
  background: url(../images/auth-bg-light.png) center center no-repeat;
  background-size: cover;
}
.light-theme .main-content.login-panel::after {
  background: transparent;
}
.light-theme .main-content.login-panel.login-panel-2 .panel-title {
  color: #464646;
}
.light-theme .main-content.login-panel .footer {
  background: #f1f4fb;
}
.light-theme .main-content.login-panel .footer p {
  color: #797979;
}
.light-theme .main-content.welcome-panel {
  background-image: url(../images/welcome-bg-light.png);
}
.light-theme .main-content.welcome-panel .static-body {
  background: rgba(255, 255, 255, 0.8);
}
.light-theme .main-content.two-factor-panel {
  background-image: url(../images/two-factor-bg-light.png);
  color: #797979;
}
.light-theme .main-content .dashboard-breadcrumb {
  background: #fff;
}
.light-theme .main-content .dashboard-breadcrumb h2 {
  color: #595959;
}
.light-theme .main-content .dashboard-filter {
  background: #fff;
}
.light-theme .main-content .dashboard-filter .form-control {
  color: #797979;
}
.light-theme .main-content .dashboard-top-box {
  color: #797979 !important;
}
.light-theme .main-content .dashboard-top-box.dashboard-top-box-2 .right {
  border-color: rgba(0, 0, 0, 0.25);
}
.light-theme .main-content .dashboard-top-box .left a {
  color: #797979;
}
.light-theme .main-content .dashboard-top-box .right .part-icon {
  background: rgba(0, 0, 0, 0.05);
}
.light-theme .main-content .dashboard-top-box:hover .right .part-icon.text-light {
  color: #fff !important;
}
.light-theme .main-content .visitor-table {
  color: #464646;
}
.light-theme .main-content .visitor-table th {
  font-weight: 500;
}
.light-theme .main-content .visitor-table tbody tr:hover {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-content .visitor-table tbody tr:hover td {
  color: #313131;
}
.light-theme .main-content .new-customer-table {
  color: #595959;
}
.light-theme .main-content .new-customer-table tr:hover td {
  color: #464646;
}
.light-theme .main-content .new-customer-table .new-customer .part-txt p {
  color: #313131;
}
.light-theme .main-content .new-customer-table .new-customer .part-txt span {
  color: #595959;
}
.light-theme .main-content .recent-order-table {
  color: #797979;
}
.light-theme .main-content .recent-order-table thead {
  background: #ebebeb;
  color: #313131;
}
.light-theme .main-content .recent-order-table thead th {
  font-weight: 600;
  white-space: nowrap;
}
.light-theme .main-content .recent-order-table tr.selected {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-content .recent-order-table tr td {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .recent-order-table .btn-box button {
  color: #797979;
}
.light-theme .main-content .recent-order-table .btn-box button:hover, .light-theme .main-content .recent-order-table .btn-box button:focus {
  color: #000;
}
.light-theme .main-content .table-bottom-control {
  border-color: rgba(0, 0, 0, 0.1);
  color: #464646;
}
.light-theme .main-content .table-bottom-control .btn {
  background: rgba(0, 0, 0, 0.07);
  color: #000;
}
.light-theme .main-content .table-bottom-control .btn:focus, .light-theme .main-content .table-bottom-control .btn.current {
  color: #fff;
}
.light-theme .main-content .table-bottom-control .previous,
.light-theme .main-content .table-bottom-control .next {
  color: #000;
  background: transparent;
}
.light-theme .main-content .table-filter-option .dataTables_length label {
  color: #464646;
}
.light-theme .main-content .digi-dataTable {
  color: #464646;
}
.light-theme .main-content .digi-dataTable thead {
  background: #ebebeb;
  color: #313131;
}
.light-theme .main-content .digi-dataTable thead th {
  font-weight: 600;
}
.light-theme .main-content .digi-dataTable tbody tr.selected {
  background: rgba(0, 0, 0, 0.05);
}
.light-theme .main-content .digi-dataTable tbody tr:hover {
  background: transparent;
}
.light-theme .main-content .digi-dataTable tr.odd > * {
  color: #313131;
}
.light-theme .main-content .digi-dataTable:not(.table-borderless) tr td {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .main-content .digi-dataTable .btn-box button:not(.btn) {
  color: #797979;
}
.light-theme .main-content .digi-dataTable .btn-box button:not(.btn):hover {
  color: #000;
}
.light-theme .main-content .table a {
  color: #464646;
}
.light-theme .main-content .recent-project-table {
  color: #595959;
}
.light-theme .main-content .recent-project-table thead {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-content .recent-project-table th {
  font-weight: 500;
}
.light-theme .main-content .recent-project-table td {
  color: #595959 !important;
}
.light-theme .main-content .table-activity {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .activity-box {
  color: #464646;
}
.light-theme .main-content .invoice-table thead {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-content .invoice-table th {
  font-weight: 500;
}
.light-theme .main-content .task-table .form-check label {
  color: #797979;
}
.light-theme .main-content .view-all-task {
  color: #464646 !important;
}
.light-theme .main-content .notice-board-table thead {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-content .notice-board-table thead th {
  font-weight: 500;
}
.light-theme .main-content .deadline-table thead {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-content .deadline-table thead th {
  font-weight: 500;
}
.light-theme .main-content .hr-recent-activity {
  color: #797979;
}
.light-theme .main-content .hr-recent-activity li .activity-name {
  color: #000;
}
.light-theme .main-content .upcoming-interview li .part-txt span {
  color: #464646;
}
.light-theme .main-content .product-table-quantity ul {
  color: #595959;
  font-weight: 600;
}
.light-theme .main-content .product-table-quantity ul .text-white {
  color: #000 !important;
}
.light-theme .main-content .perma-txt {
  color: #464646;
}
.light-theme .main-content .perma-txt .btn-flush {
  color: #fff;
}
.light-theme .main-content .product-image-upload .part-txt {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .main-content .product-image-upload .part-txt h5 {
  color: #464646;
}
.light-theme .main-content .product-image-upload .part-txt h5 span {
  color: #A9B4CC;
}
.light-theme .main-content .jquery-uploader-preview-container .jquery-uploader-select-card,
.light-theme .main-content .jquery-uploader-preview-container .jquery-uploader-card {
  background: rgba(0, 0, 0, 0.03);
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .main-content .jquery-uploader-preview-container .jquery-uploader-select-card .upload-button,
.light-theme .main-content .jquery-uploader-preview-container .jquery-uploader-card .upload-button {
  color: #464646;
}
.light-theme .main-content .jquery-uploader-preview-container .jquery-uploader-select-card:hover,
.light-theme .main-content .jquery-uploader-preview-container .jquery-uploader-card:hover {
  border-color: rgba(0, 0, 0, 0.5);
}
.light-theme .main-content .product-data-tab .input-additional-txt,
.light-theme .main-content .product-data-tab label {
  color: #595959;
}
.light-theme .main-content .publish-date {
  color: #313131;
}
.light-theme .main-content .publish-date input {
  color: #797979;
}
.light-theme .main-content .product-tag-area .input-txt {
  color: #797979;
}
.light-theme .main-content .product-tag-area .all-tags .item {
  background: rgba(0, 0, 0, 0.1);
  color: #464646;
}
.light-theme .main-content .table-product-card .part-txt span {
  color: #797979;
}
.light-theme .main-content .table-product-card .part-txt span.product-name {
  color: #464646;
}
.light-theme .main-content .table-category-card .part-icon span {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .main-content .chatting-panel .active-members .owl-nav {
  color: #464646;
}
.light-theme .main-content .chatting-panel .active-members .owl-nav button {
  background: -webkit-gradient(linear, left top, right top, color-stop(30%, #fff), to(transparent));
  background: linear-gradient(90deg, #fff 30%, transparent);
}
.light-theme .main-content .chatting-panel .active-members .owl-nav button.owl-next {
  background: -webkit-gradient(linear, right top, left top, color-stop(30%, #fff), to(transparent));
  background: linear-gradient(-90deg, #fff 30%, transparent);
}
.light-theme .main-content .chatting-panel .active-members .owl-nav button:hover {
  color: #000;
}
.light-theme .main-content .chatting-panel .message-list .single-message {
  background: rgba(0, 0, 0, 0.03);
  color: #797979;
}
.light-theme .main-content .chatting-panel .message-list .single-message .part-txt .user-name {
  color: #464646;
  font-weight: 500;
}
.light-theme .main-content .chatting-panel .message-list .single-message:hover {
  background: rgba(0, 0, 0, 0.07);
}
.light-theme .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt {
  color: #464646;
}
.light-theme .main-content .chatting-panel .chatting-area .chat-top-bar .search-in-chat {
  background: #fff;
}
.light-theme .main-content .chatting-panel .msg-area {
  background: #f1f4fb;
}
.light-theme .main-content .chatting-panel .msg-area::after {
  background: url(../images/chat-bg-black.png) center center no-repeat;
  background-size: cover;
}
.light-theme .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner {
  background: #fff;
}
.light-theme .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner:last-child::after {
  border-bottom-color: #fff;
}
.light-theme .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner p {
  color: #464646;
}
.light-theme .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner .sent-status {
  color: #595959;
}
.light-theme .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option button {
  color: #000;
  opacity: 1;
}
.light-theme .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option .msg-time {
  color: #464646;
  opacity: 1;
}
.light-theme .main-content .chatting-panel .msg-area .day-divider span {
  color: #464646;
}
.light-theme .main-content .chatting-panel .msg-area .day-divider span::before, .light-theme .main-content .chatting-panel .msg-area .day-divider span::after {
  border-color: rgba(0, 0, 0, 0.2);
}
.light-theme .main-content .chatting-panel .user-short .part-txt {
  color: #464646;
}
.light-theme .main-content .chatting-panel .chatting-option {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-content .chatting-panel .chatting-option ul li a {
  color: #797979;
}
.light-theme .main-content .chatting-panel .chatting-option ul li a:hover {
  color: #000;
}
.light-theme .main-content .email-panel .emial-menu-list ul li {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .email-panel .emial-menu-list ul li button.active .part-icon,
.light-theme .main-content .email-panel .emial-menu-list ul li button.active .part-txt, .light-theme .main-content .email-panel .emial-menu-list ul li button:hover .part-icon,
.light-theme .main-content .email-panel .emial-menu-list ul li button:hover .part-txt {
  color: #fff;
}
.light-theme .main-content .email-panel .emial-menu-list ul li button .part-icon {
  color: #464646;
}
.light-theme .main-content .email-panel .emial-menu-list ul li button .part-txt {
  color: #797979;
}
.light-theme .main-content .email-panel .email-table .unread .table-txt {
  color: #464646;
}
.light-theme .main-content .email-panel .table-bottom-control {
  background: #ebebeb;
}
.light-theme .main-content .invoice {
  color: #464646;
}
.light-theme .main-content .invoice .table {
  color: #797979;
}
.light-theme .main-content .invoice .total-payment-area ul li:last-child {
  color: #000;
}
.light-theme .main-content .login-body {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .login-body .top {
  border-color: rgba(0, 0, 0, 0.07);
}
.light-theme .main-content .login-body .top a {
  color: #464646;
}
.light-theme .main-content .login-body .bottom .panel-title {
  color: #464646;
  font-weight: 500;
}
.light-theme .main-content .login-body .bottom .form-control {
  border: 1px solid rgba(0, 0, 0, 0.15);
}
.light-theme .main-content .login-body .bottom .other-option p {
  color: #797979;
}
.light-theme .main-content .login-body .bottom .other-option .social-box a {
  background: #f5f5f5;
}
.light-theme .main-content .login-body .text-white {
  color: #595959 !important;
}
.light-theme .main-content .static-body {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .static-body .part-txt h2 {
  color: #464646;
}
.light-theme .main-content .static-body .part-txt p {
  color: #797979;
}
.light-theme .main-content .verification-area {
  color: #797979;
}
.light-theme .main-content .verification-area .otp-fake-input .otp-content {
  color: #464646;
}
.light-theme .main-content .all-steps {
  background: rgba(255, 255, 255, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .all-steps .account-types .form-check label {
  background: rgba(0, 0, 0, 0.03);
}
.light-theme .main-content .all-steps .account-types .form-check label .part-icon {
  color: #595959;
}
.light-theme .main-content .all-steps .account-types .form-check label .title {
  color: #595959;
}
.light-theme .main-content .all-steps .account-types .form-check label .dscr {
  color: #797979;
}
.light-theme .main-content .all-steps .account-types .form-check input:checked + label {
  background: rgba(0, 0, 0, 0.06);
}
.light-theme .main-content .all-steps .account-info-title {
  color: #313131;
}
.light-theme .main-content .all-steps .team-size .form-check label {
  background: rgba(0, 0, 0, 0.03);
  color: #313131;
}
.light-theme .main-content .all-steps .team-size .form-check input:checked + label {
  background: rgba(0, 0, 0, 0.07);
}
.light-theme .main-content .all-steps .form-control {
  color: #313131;
}
.light-theme .main-content .profile-sidebar-title {
  color: #464646;
}
.light-theme .main-content .profile-sidebar-subtitle {
  color: #464646;
}
.light-theme .main-content .profile-sidebar .top .part-txt h4 {
  color: #464646;
}
.light-theme .main-content .profile-sidebar .top .part-txt span {
  color: #797979;
}
.light-theme .main-content .profile-sidebar .top .part-txt .admin-social a {
  color: #797979;
}
.light-theme .main-content .profile-sidebar .bottom ul li {
  color: #797979;
}
.light-theme .main-content .profile-sidebar .bottom ul li span {
  color: #464646;
}
.light-theme .main-content .profile-sidebar .bottom p {
  color: #464646;
}
.light-theme .main-content .user-activity ul li {
  color: #464646;
}
.light-theme .main-content .user-activity ul li::after {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .main-content .user-activity ul li .left,
.light-theme .main-content .user-activity ul li .right {
  background: #fff;
}
.light-theme .main-content .user-activity-details,
.light-theme .main-content .user-activity-date {
  color: #797979;
}
.light-theme .main-content .profile-edit-tab-title::after {
  background: rgba(0, 0, 0, 0.25);
  opacity: 1;
}
.light-theme .main-content .profile-edit-tab-title h6 {
  color: #464646;
  background: #fff;
}
.light-theme .main-content .profile-edit-tab .admin-profile .admin-name {
  color: #464646;
}
.light-theme .main-content .profile-edit-tab .admin-profile .admin-role {
  color: #595959;
}
.light-theme .main-content .icon-row .icon-col .icon-box {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .icon-row .icon-col .icon-box span.icon {
  color: #464646;
}
.light-theme .main-content .file-manager-sidebar {
  color: #464646;
}
.light-theme .main-content .file-manager-sidebar nav button {
  color: #797979;
}
.light-theme .main-content .file-manager-sidebar nav button.active {
  color: #464646;
}
.light-theme .main-content .file-manager-sidebar .connected-app .file-manager-sidebar-title {
  color: #000;
}
.light-theme .main-content .file-manager-sidebar .connected-app li a {
  color: #595959;
}
.light-theme .main-content .file-manager-card {
  background: #f5f5f5;
  color: #595959;
  -webkit-box-shadow: 0 0;
          box-shadow: 0 0;
}
.light-theme .main-content .file-manager-card .top .action {
  color: #464646;
}
.light-theme .main-content .file-manager-card .bottom .left .folder-name {
  color: #464646;
}
.light-theme .main-content .file-card {
  background: #f5f5f5;
}
.light-theme .main-content .file-card .part-txt {
  color: #464646;
  border-color: rgba(0, 0, 0, 0.07);
}
.light-theme .main-content .file-card .part-txt .file-name {
  color: #464646;
}
.light-theme .main-content .file-manager-row .file-manager-col-head {
  background: #ebebeb;
}
.light-theme .main-content .file-manager-row .file-manager-col-head span {
  color: #464646;
}
.light-theme .main-content .file-manager-row.list-view .file-manager-col .file-card {
  border-color: rgba(0, 0, 0, 0.07);
}
.light-theme .main-content .file-manager-row.list-view .file-manager-col .file-card .file-type {
  color: #464646;
}
.light-theme .main-content .part-btn p {
  color: #595959;
}
.light-theme .main-content .file-manager-user .part-txt span {
  color: #595959;
}
.light-theme .main-content .file-manager-settings-subtitle {
  color: #313131;
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .plan-name {
  color: #464646;
}
.light-theme .main-content .used-storage {
  color: #595959;
}
.light-theme .main-content .file-manager-settings-table {
  color: #595959;
}
.light-theme .main-content .error-panel {
  color: #464646;
}
.light-theme .main-content .pricing-panel-2 .pricing-table {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .pricing-panel-2 .pricing-table .middle {
  background: rgba(0, 0, 0, 0.03);
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .main-content .pricing-panel-2 .pricing-table .middle ul {
  color: #797979;
}
.light-theme .footer {
  background: #fff;
}
.light-theme .footer p {
  color: #595959;
}
.light-theme .modal .ck.ck-editor__main > .ck-editor__editable,
.light-theme .main-content .ck.ck-editor__main > .ck-editor__editable,
.light-theme .compose-mail .ck.ck-editor__main > .ck-editor__editable,
.light-theme .mail-details .ck.ck-editor__main > .ck-editor__editable {
  background: #f5f5f5;
  color: #464646;
}
.light-theme .modal .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph,
.light-theme .main-content .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph,
.light-theme .compose-mail .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph,
.light-theme .mail-details .ck.ck-editor__main > .ck-editor__editable .ck-table-bogus-paragraph {
  color: #464646;
}
.light-theme .modal .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused),
.light-theme .main-content .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused),
.light-theme .compose-mail .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused),
.light-theme .mail-details .ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .modal .ck.ck-toolbar,
.light-theme .main-content .ck.ck-toolbar,
.light-theme .compose-mail .ck.ck-toolbar,
.light-theme .mail-details .ck.ck-toolbar {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .modal .ck.ck-input,
.light-theme .main-content .ck.ck-input,
.light-theme .compose-mail .ck.ck-input,
.light-theme .mail-details .ck.ck-input {
  border-color: rgba(0, 0, 0, 0.15);
  color: #464646;
}
.light-theme .modal .ck-labeled-field-view__status,
.light-theme .main-content .ck-labeled-field-view__status,
.light-theme .compose-mail .ck-labeled-field-view__status,
.light-theme .mail-details .ck-labeled-field-view__status {
  color: #595959 !important;
}
.light-theme .modal .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
.light-theme .main-content .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
.light-theme .compose-mail .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label,
.light-theme .mail-details .ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {
  background: #f5f5f5;
  color: #595959;
}
.light-theme .modal .ck-insert-table-dropdown__label, .light-theme .modal .ck.ck-button .ck-button__label, .light-theme .modal a.ck.ck-button .ck-button__label,
.light-theme .main-content .ck-insert-table-dropdown__label,
.light-theme .main-content .ck.ck-button .ck-button__label,
.light-theme .main-content a.ck.ck-button .ck-button__label,
.light-theme .compose-mail .ck-insert-table-dropdown__label,
.light-theme .compose-mail .ck.ck-button .ck-button__label,
.light-theme .compose-mail a.ck.ck-button .ck-button__label,
.light-theme .mail-details .ck-insert-table-dropdown__label,
.light-theme .mail-details .ck.ck-button .ck-button__label,
.light-theme .mail-details a.ck.ck-button .ck-button__label {
  color: #797979 !important;
}
.light-theme .modal .ck.ck-toolbar .ck.ck-toolbar__separator,
.light-theme .main-content .ck.ck-toolbar .ck.ck-toolbar__separator,
.light-theme .compose-mail .ck.ck-toolbar .ck.ck-toolbar__separator,
.light-theme .mail-details .ck.ck-toolbar .ck.ck-toolbar__separator {
  background: rgba(0, 0, 0, 0.15);
}
.light-theme .modal .ck.ck-button.ck-on:not(.ck-disabled):hover, .light-theme .modal a.ck.ck-button.ck-on:not(.ck-disabled):hover,
.light-theme .modal .ck.ck-button.ck-on, .light-theme .modal a.ck.ck-button.ck-on,
.light-theme .modal .ck.ck-button:not(.ck-disabled):hover, .light-theme .modal a.ck.ck-button:not(.ck-disabled):hover,
.light-theme .main-content .ck.ck-button.ck-on:not(.ck-disabled):hover,
.light-theme .main-content a.ck.ck-button.ck-on:not(.ck-disabled):hover,
.light-theme .main-content .ck.ck-button.ck-on,
.light-theme .main-content a.ck.ck-button.ck-on,
.light-theme .main-content .ck.ck-button:not(.ck-disabled):hover,
.light-theme .main-content a.ck.ck-button:not(.ck-disabled):hover,
.light-theme .compose-mail .ck.ck-button.ck-on:not(.ck-disabled):hover,
.light-theme .compose-mail a.ck.ck-button.ck-on:not(.ck-disabled):hover,
.light-theme .compose-mail .ck.ck-button.ck-on,
.light-theme .compose-mail a.ck.ck-button.ck-on,
.light-theme .compose-mail .ck.ck-button:not(.ck-disabled):hover,
.light-theme .compose-mail a.ck.ck-button:not(.ck-disabled):hover,
.light-theme .mail-details .ck.ck-button.ck-on:not(.ck-disabled):hover,
.light-theme .mail-details a.ck.ck-button.ck-on:not(.ck-disabled):hover,
.light-theme .mail-details .ck.ck-button.ck-on,
.light-theme .mail-details a.ck.ck-button.ck-on,
.light-theme .mail-details .ck.ck-button:not(.ck-disabled):hover,
.light-theme .mail-details a.ck.ck-button:not(.ck-disabled):hover {
  background: rgba(0, 0, 0, 0.1);
}
.light-theme .modal .ck.ck-dropdown__panel,
.light-theme .main-content .ck.ck-dropdown__panel,
.light-theme .compose-mail .ck.ck-dropdown__panel,
.light-theme .mail-details .ck.ck-dropdown__panel {
  background: #f5f5f5;
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .modal .ck.ck-dropdown__panel .ck.ck-list button span,
.light-theme .main-content .ck.ck-dropdown__panel .ck.ck-list button span,
.light-theme .compose-mail .ck.ck-dropdown__panel .ck.ck-list button span,
.light-theme .mail-details .ck.ck-dropdown__panel .ck.ck-list button span {
  color: #464646 !important;
}
.light-theme .modal .ck-button,
.light-theme .main-content .ck-button,
.light-theme .compose-mail .ck-button,
.light-theme .mail-details .ck-button {
  border-color: rgba(0, 0, 0, 0.1) !important;
}
.light-theme .modal .ck.ck-list__item .ck-button.ck-on,
.light-theme .main-content .ck.ck-list__item .ck-button.ck-on,
.light-theme .compose-mail .ck.ck-list__item .ck-button.ck-on,
.light-theme .mail-details .ck.ck-list__item .ck-button.ck-on {
  background: #f1f4fb;
}
.light-theme .modal .ck.ck-list__item .ck-button:hover:not(.ck-disabled),
.light-theme .main-content .ck.ck-list__item .ck-button:hover:not(.ck-disabled),
.light-theme .compose-mail .ck.ck-list__item .ck-button:hover:not(.ck-disabled),
.light-theme .mail-details .ck.ck-list__item .ck-button:hover:not(.ck-disabled) {
  background: rgba(0, 0, 0, 0.1);
}
.light-theme .modal .ck.ck-list__item .ck-button.ck-on:hover:not(.ck-disabled),
.light-theme .main-content .ck.ck-list__item .ck-button.ck-on:hover:not(.ck-disabled),
.light-theme .compose-mail .ck.ck-list__item .ck-button.ck-on:hover:not(.ck-disabled),
.light-theme .mail-details .ck.ck-list__item .ck-button.ck-on:hover:not(.ck-disabled) {
  background: #f1f4fb;
}
.light-theme .ck.ck-button svg circle,
.light-theme .ck.ck-button svg path {
  fill: #797979 !important;
}
.light-theme .ck.ck-link-actions .ck-button.ck-link-actions__preview .ck-button__label {
  color: #464646 !important;
}
.light-theme .ck.ck-balloon-panel {
  background: #f5f5f5 !important;
  border: 0 !important;
  z-index: 9999 !important;
}
.light-theme .ck.ck-balloon-panel[class*=arrow_n]::before, .light-theme .ck.ck-balloon-panel[class*=arrow_n]::after {
  border-color: transparent transparent #f5f5f5 transparent !important;
}
.light-theme .ck.ck-balloon-panel input {
  border-color: rgba(255, 255, 255, 0.15);
  color: #464646;
}
.light-theme .ck.ck-balloon-panel label {
  background: #f5f5f5 !important;
  color: #464646;
}
.light-theme .ck.ck-balloon-panel button {
  border-color: rgba(0, 0, 0, 0.1) !important;
}
.light-theme .ck.ck-balloon-panel button:hover:not(.ck-disabled) {
  background: rgba(0, 0, 0, 0.1);
}
.light-theme .ck.ck-balloon-panel .ck.ck-toolbar {
  background: #242526;
}
.light-theme .ck.ck-balloon-panel .ck.ck-toolbar .ck-button:focus, .light-theme .ck.ck-balloon-panel .ck.ck-toolbar .ck-button:active, .light-theme .ck.ck-balloon-panel .ck.ck-toolbar .ck-button.active, .light-theme .ck.ck-balloon-panel .ck.ck-toolbar .ck-button.ck-on {
  background: rgba(255, 255, 255, 0.1) !important;
}
.light-theme .view-task h6 {
  color: #464646;
}
.light-theme .view-task .task-created a {
  color: #464646;
}
.light-theme .view-task .task-dscr .task-attachment .attched-box {
  background: rgba(0, 0, 0, 0.03);
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .view-task .comments .single-comment .part-txt {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .view-task .task-info-box {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .view-task .task-info-box h6 {
  border-color: rgba(0, 0, 0, 0.1);
}
.light-theme .voice-call .user .user-name {
  color: #464646;
}
.light-theme .voice-call .user .call-status {
  color: #464646;
}
.light-theme .voice-call .call-option {
  background: #f1f4fb;
}
.light-theme .video-call .bottom .call-option {
  background: #f1f4fb;
}
.light-theme .mail-details {
  border-color: rgba(0, 0, 0, 0.15);
  color: #464646;
}
.light-theme .mail-details .single-mail {
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .mail-details .single-mail .mail-top .mail-profile .part-txt span.name {
  color: #464646;
}
.light-theme .mail-details .single-mail .mail-top .mail-profile .part-txt span.mail {
  color: #A9B4CC;
}
.light-theme .mail-details .mail-reply-option {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.15);
}
.light-theme .mail-details-expanded {
  background: #fff;
}
.light-theme .file-details li {
  color: #797979;
}
.light-theme .file-details li span {
  color: #464646;
}

@media only screen and (max-width: 1799px) and (min-width: 1200px) {
  /* ======== Global Responsive Styles ======== */
  body {
    overflow-x: hidden;
  }
  body.body-padding {
    padding-left: 230px;
  }
  body.body-padding.expanded.has-fixed-sidebar {
    padding-left: 60px;
  }
  body.body-padding.expanded .main-content {
    width: 100%;
  }
  body.body-padding.hover-menu {
    padding-left: 60px;
  }
  body.body-p-top {
    padding-top: 60px;
  }
  body.body-p-top.has-horizontal {
    padding-top: 109px;
  }
  body.has-two-column-menu {
    padding-left: 230px;
  }
  body .main-content {
    overflow: hidden;
  }
  .row {
    --bs-gutter-y: 15px;
    --bs-gutter-x: 15px;
  }
  .g-3, .gy-3 {
    --bs-gutter-y: 10px;
  }
  .g-3, .gx-3 {
    --bs-gutter-x: 10px;
  }
  .g-2, .gy-2 {
    --bs-gutter-y: 8px;
  }
  .g-2, .gx-2 {
    --bs-gutter-x: 8px;
  }
  .g-1, .gy-1 {
    --bs-gutter-y: 5px;
  }
  .g-1, .gx-1 {
    --bs-gutter-x: 5px;
  }
  .g-0, .gy-0 {
    --bs-gutter-y: 0;
  }
  .g-0, .gx-0 {
    --bs-gutter-x: 0;
  }
  .mb-25 {
    margin-bottom: 15px;
  }
  .panel.chart-panel-1 .panel-body {
    padding-right: 5px;
    padding-left: 0;
    margin-left: -3px;
  }
  .panel .panel-header {
    height: 50px;
  }
  .panel .panel-header h5 {
    font-size: 16px;
    line-height: 50px;
  }
  /* ======== Header Section Responsive ======== */
  .header {
    height: 60px;
  }
  .header .gap-20 {
    gap: 10px;
  }
  .header.expanded .main-logo {
    width: 60px;
  }
  .main-logo {
    width: 230px;
    height: 60px;
  }
  .header-right-btns {
    gap: 10px;
    padding-right: 10px;
  }
  .header-right-btns .header-form:focus-within input {
    width: 200px;
  }
  .header-right-btns .lang-select {
    font-size: 14px;
  }
  .header-btn {
    font-size: 20px;
  }
  /* ======== Main Sidebar Section Responsive ======== */
  .main-sidebar {
    width: 230px;
    padding-top: 60px;
  }
  .main-sidebar::after {
    width: 230px;
    border-right: 1px solid rgba(255, 255, 255, 0.15);
  }
  .main-sidebar.sidebar-mini {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  .main-sidebar.collapsed {
    width: 60px;
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  .main-sidebar.collapsed.two-column-menu {
    width: 230px;
    padding-top: 60px;
    -webkit-transition: none;
    transition: none;
  }
  .main-sidebar.collapsed.two-column-menu.sub-menu-collapsed {
    width: 60px;
  }
  .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu[id] {
    width: 180px;
    padding: 5px 0;
  }
  .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link {
    padding: 5px 10px;
    font-size: 13px;
  }
  .main-sidebar.collapsed .main-menu {
    padding: 5px 1px 5px 5px;
  }
  .main-sidebar.collapsed .os-content {
    gap: 5px;
  }
  .main-sidebar.collapsed .sidebar-menu {
    padding-right: 4px;
  }
  .main-sidebar.collapsed .sidebar-menu .os-scrollbar-vertical {
    width: 3px;
  }
  .main-sidebar.collapsed .sidebar-item {
    width: 50px;
  }
  .main-sidebar.collapsed .sidebar-item .sidebar-link {
    padding: 5px;
    line-height: 2;
    text-align: center;
    height: 40px;
  }
  .main-sidebar.collapsed .sidebar-item .sidebar-link .nav-icon {
    width: auto;
  }
  .main-sidebar.horizontal-menu {
    top: 60px;
    -webkit-transform: translate(0);
            transform: translate(0);
  }
  .main-sidebar.flush-menu.sidebar-hover .sidebar-item, .main-sidebar.flush-menu.collapsed .sidebar-item {
    width: 60px;
  }
  .main-sidebar.flush-menu.sidebar-hover .sidebar-item .sidebar-link, .main-sidebar.flush-menu.collapsed .sidebar-item .sidebar-link {
    line-height: 3;
  }
  .main-sidebar.flush-menu.sidebar-hover .sidebar-item .sidebar-link .nav-icon, .main-sidebar.flush-menu.collapsed .sidebar-item .sidebar-link .nav-icon {
    line-height: 2.5;
  }
  .main-sidebar.flush-menu .main-menu {
    padding-left: 0;
    padding-right: 0;
  }
  .main-sidebar.sidebar-hover {
    width: 60px;
  }
  .main-sidebar.sidebar-hover .main-menu {
    padding: 5px 0 5px 5px;
  }
  .main-sidebar.sidebar-hover .main-menu .sidebar-menu {
    padding-right: 5px;
  }
  .main-menu {
    padding: 10px 0 10px 10px;
  }
  .sidebar-link-group {
    padding: 0;
  }
  .sidebar-item {
    font-size: 13px;
    font-weight: 400;
    margin-bottom: 10px;
  }
  .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active {
    font-weight: 500;
  }
  .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active::after {
    width: 8px;
  }
  .sidebar-menu .help-center {
    padding: 20px 10px;
  }
  .sidebar-menu .help-center h3 {
    font-size: 16px;
    margin-top: -3px;
    margin-bottom: 14px;
  }
  .sidebar-menu .help-center p {
    font-size: 12px;
    margin-bottom: 15px;
  }
  /* ======== Main Content Section Responsive ======== */
  .main-content {
    padding: 15px 15px 60px;
  }
  .main-content .dashboard-breadcrumb.mb-25 {
    margin-bottom: 20px;
  }
  .main-content .dashboard-breadcrumb h2 {
    font-size: 20px;
    line-height: 17px;
  }
  .main-content .dashboard-top-box {
    padding: 10px;
  }
  .main-content .dashboard-top-box .left h3 {
    font-size: 14px;
    font-weight: 500;
    margin-top: -2px;
    margin-bottom: 7px;
  }
  .main-content .dashboard-top-box .left p {
    font-size: 12px;
  }
  .main-content .dashboard-top-box .left a {
    font-size: 10px;
  }
  .main-content .new-customer-table .new-customer .part-img {
    width: 55px;
    height: 55px;
  }
  .main-content .recent-order-table {
    font-size: 12px;
    max-width: none;
    min-width: -webkit-max-content !important;
    min-width: -moz-max-content !important;
    min-width: max-content !important;
  }
  .main-content .table-bottom-control {
    padding: 15px;
  }
  .main-content .jquery-uploader-preview-container .jquery-uploader-select-card,
  .main-content .jquery-uploader-preview-container .jquery-uploader-card {
    width: 180px;
    height: 180px;
  }
  .main-content .chatting-panel {
    width: calc(100% - 30px);
    height: calc(100% - 75px);
  }
  .main-content .chatting-panel .msg-area {
    height: calc(100% - 145px);
  }
  .main-content .email-panel {
    width: calc(100% - 30px);
    height: calc(100% - 75px);
  }
  .main-content .email-panel > .panel .panel-body:not(.email-menu) {
    height: calc(100% - 50px);
  }
  .main-content .email-panel .table-wrapper {
    height: calc(100% - 135px);
  }
  .main-content .email-panel .table-wrapper .dataTables_scrollBody {
    min-height: calc(100% - 48px);
  }
  .main-content .email-panel .tab-pane .tab-content.email-tab-content {
    height: calc(100% - 135px);
  }
  .main-content .email-panel .inbox-tab {
    gap: 5px !important;
  }
  .main-content .email-panel .mail-details {
    position: fixed;
    top: 75px;
    left: 245px;
    right: 15px;
    bottom: 15px;
    width: calc(100% - 260px);
    background: #112143;
    border-radius: 3px;
    padding: 10px;
    z-index: 13;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-profile .part-txt span {
    font-size: 12px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-profile .part-txt span.mail {
    font-size: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-action {
    gap: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-action .date {
    font-size: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-action button {
    font-size: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-body p {
    font-size: 12px;
  }
  .main-content .email-panel .mail-body-top .right {
    display: none !important;
  }
  .main-content .email-panel .mail-menu-btn {
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  .main-content .profile-edit-tab-title h6 {
    margin-bottom: 8px;
  }
  .main-content .profile-edit-tab .admin-profile .image-wrap {
    width: 100px;
    margin-bottom: 20px;
  }
  .main-content .profile-edit-tab .admin-profile .image-wrap .image-change {
    right: 10px;
    width: 25px;
    height: 25px;
    font-size: 12px;
  }
  .main-content .icon-row .icon-col {
    width: calc(14.2857142857% - 17.2px);
  }
  .main-content .file-card .part-img {
    height: 100px;
  }
  /* dark theme responsive design */
  .dark-theme .main-content .email-panel .mail-details {
    background: #242526;
  }
  .dark-theme .main-content .email-panel .mail-details .mail-reply-option {
    background: #242526;
  }
  .dark-theme .main-content .file-manager-sidebar-col {
    background: #242526;
  }
  /* light theme responsive design */
  .light-theme .main-content .email-panel .mail-details {
    background: #fff;
  }
  .light-theme .main-content .email-panel .mail-details .mail-reply-option {
    background: #fff;
  }
}
@media only screen and (max-width: 1399px) and (min-width: 1200px) {
  /* ======== Main Content Section Responsive ======== */
  .main-content.login-panel.login-panel-2 .panel-title {
    font-size: 30px;
    margin-bottom: 30px;
  }
  .main-content.login-panel.login-panel-3 .container {
    background-size: contain;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left {
    padding: 15px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left p {
    margin-top: -1px;
    margin-bottom: 3px !important;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left p.text-muted {
    margin-bottom: -3px !important;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left h3 {
    margin-bottom: 12px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .right {
    height: 100%;
    padding: 15px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .right .part-icon {
    width: 50px;
    height: 50px;
    line-height: 50px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .right .part-icon span {
    font-size: 20px;
  }
  .main-content .new-customer-table tr td {
    padding: 7.5px 0;
  }
  .main-content .new-customer-table .new-customer .part-img {
    width: 45px;
    height: 45px;
  }
  .main-content .jquery-uploader-preview-container .jquery-uploader-select-card,
  .main-content .jquery-uploader-preview-container .jquery-uploader-card {
    width: 160px;
    height: 160px;
  }
  .main-content .chatting-panel .panel:first-child {
    width: 30%;
  }
  .main-content .chatting-panel .panel:nth-child(2) {
    width: 70%;
  }
  .main-content .chatting-panel .panel:last-child {
    position: absolute;
    right: 0;
    width: 30%;
    border: 0 !important;
    -webkit-transition: all 0.1s ease-in;
    transition: all 0.1s ease-in;
    z-index: 11;
  }
  .main-content .chatting-panel .panel:last-child.closed {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
  .main-content .chatting-panel .avatar {
    width: 30px;
    height: 30px;
  }
  .main-content .chatting-panel .message-list .single-message {
    padding: 10px;
    margin-bottom: 10px;
  }
  .main-content .chatting-panel .message-list .single-message:last-child {
    margin-bottom: 0;
  }
  .main-content .chatting-panel .message-list .single-message.unread::after {
    left: 3px;
    width: 4px;
    height: 4px;
  }
  .main-content .chatting-panel .message-list .single-message .avatar {
    width: 30px;
    height: 30px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt {
    width: calc(100% - 40px);
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .top {
    margin-top: -1px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .user-name {
    font-size: 12px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .msg-time {
    font-size: 10px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .msg-short {
    font-size: 12px;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user {
    gap: 5px;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt {
    gap: 1px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .user-name {
    font-size: 14px;
    font-weight: 400;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .active-status {
    font-size: 10px;
    padding-left: 10px;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .active-status::after {
    width: 7px;
    height: 7px;
  }
  .main-content .chatting-panel .msg-area .single-message {
    max-width: 75%;
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner .sent-status {
    font-size: 10px;
  }
  .main-content .chatting-panel .msg-area .single-message .avatar {
    display: none;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box {
    max-width: 100%;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner {
    padding: 10px;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner:last-child::after {
    display: none;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner p {
    font-size: 12px;
    letter-spacing: 0;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option {
    left: calc(100% + 5px);
    gap: 7px;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option .msg-time {
    font-size: 10px;
  }
  .main-content .chatting-panel .msg-area .day-divider {
    margin-bottom: 8px;
  }
  .main-content .chatting-panel .msg-area .day-divider span {
    font-size: 12px;
  }
  .main-content .chatting-panel .msg-type-area form .chat-input {
    width: calc(100% - 80px);
  }
  .main-content .chatting-panel .user-short .back-to-chat-btn {
    position: absolute;
    top: 10px;
    left: 10px;
  }
  .main-content .chatting-panel .user-short .avatar {
    width: 50px;
    height: 50px;
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .user-short .part-txt {
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .user-short .part-txt span.user-name {
    font-size: 14px;
    margin-top: -1px;
    margin-bottom: 7px;
  }
  .main-content .chatting-panel .user-short .part-txt span.user-mail {
    font-size: 12px;
  }
  .main-content .chatting-panel .user-short .user-option {
    gap: 10px;
  }
  .main-content .chatting-panel .chatting-option {
    padding: 10px;
  }
  .main-content .chatting-panel .chatting-option.mb-20 {
    margin-bottom: 10px;
  }
  .main-content .chatting-panel .chatting-option ul li {
    font-size: 14px;
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .chatting-option ul li a span {
    margin-right: 5px;
  }
  .main-content .chatting-panel .chatting-panel-top-btns {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 15px;
  }
  .main-content .chatting-panel .chatting-panel-top-btns .btn.btn-icon {
    width: 25px;
    height: 25px;
    line-height: 25px;
    font-size: 14px;
  }
  .main-content .chatting-panel .chatting-panel-top-btns > .btn.btn-icon {
    width: auto;
    height: auto;
    border: 0;
    background: transparent;
  }
  .main-content .chatting-panel .chatting-panel-top-btns > .btn.btn-icon:active, .main-content .chatting-panel .chatting-panel-top-btns > .btn.btn-icon:focus {
    background-color: transparent !important;
    -webkit-box-shadow: 0 0;
            box-shadow: 0 0;
  }
  .main-content .email-panel .tab-pane .tab-content.email-tab-content {
    height: calc(100% - 176px);
  }
  .main-content .email-panel .tab-pane .tab-content.email-tab-content .table-wrapper .dataTables_scrollBody {
    min-height: calc(100% - 94px);
  }
  .main-content .profile-sidebar .top {
    padding-top: 15px;
    padding-bottom: 25px;
    gap: 10px;
  }
  .main-content .profile-sidebar .top .part-img {
    width: 80px;
  }
  .main-content .profile-sidebar .top .image-wrap .image-change {
    right: 10px;
    width: 25px;
    height: 25px;
    font-size: 12px;
  }
  .main-content .profile-sidebar .top .part-txt {
    width: calc(100% - 90px);
  }
  .main-content .profile-sidebar .top .part-txt h4 {
    font-size: 14px;
    margin-bottom: 5px;
  }
  .main-content .profile-sidebar .top .part-txt span {
    font-size: 12px;
    margin-bottom: 8px;
  }
  .main-content .profile-sidebar .top .part-txt .admin-social {
    font-size: 12px;
  }
  .main-content .profile-sidebar .bottom ul {
    margin-bottom: 12px;
  }
  .main-content .profile-sidebar .bottom ul li {
    font-size: 12px;
    line-height: 25px;
  }
  .main-content .profile-sidebar .bottom p {
    font-size: 14px;
    line-height: 24px;
    margin-top: -12px;
  }
  .main-content .profile-sidebar-title {
    font-size: 16px;
    font-weight: 500;
  }
  .main-content .profile-sidebar-subtitle {
    font-size: 14px;
    margin-top: -2px;
    margin-bottom: 18px;
  }
  .main-content .user-activity ul li {
    margin-bottom: 20px;
  }
  .main-content .user-activity ul li .left {
    padding-left: 15px;
    padding-right: 10px;
  }
  .main-content .user-activity ul li .left::after {
    left: 1px;
    width: 11px;
    height: 11px;
  }
  .main-content .user-activity-title {
    font-size: 12px;
    line-height: 1.2;
    margin-top: -2px;
    margin-bottom: 8px;
  }
  .main-content .user-activity-details {
    font-size: 12px;
    margin-bottom: 1px;
  }
  .main-content .user-activity-date {
    font-size: 10px;
    margin-bottom: -4px;
  }
  .main-content .user-activity-time {
    font-size: 10px;
    padding-left: 5px;
  }
  .main-content .icon-row .icon-col {
    width: calc(16.6666666667% - 16.7px);
  }
  .main-content .file-manager-row {
    gap: 15px;
  }
  .main-content .file-manager-row .file-manager-col {
    width: calc(25% - 11.3px);
  }
  .main-content .file-card .part-img {
    height: 90px;
  }
  .main-content .all-steps {
    right: 50px;
    width: calc(100% - 500px);
    height: calc(100% - 150px);
  }
  .main-content .error-panel .part-img {
    max-height: 250px;
  }
  .main-content .error-panel .part-img img {
    max-height: 100%;
  }
  .main-content .error-panel .error-title {
    font-size: 80px;
  }
  .main-content .error-panel .error-subtitle {
    font-size: 30px;
  }
  .main-content .static-body {
    width: 500px;
  }
  .main-content .static-body .panel-body {
    padding: 30px;
  }
  .main-content .static-body .logo {
    margin-bottom: 30px;
  }
  .main-content .static-body .part-txt {
    margin-bottom: 30px;
  }
  .main-content .static-body .part-txt h2 {
    font-size: 25px;
    margin-bottom: 16px;
  }
  .main-content .static-body .part-txt p {
    margin-bottom: 19px;
  }
  .main-content .static-body .part-img {
    padding: 0 50px;
  }
  .main-content .login-body {
    width: 450px;
  }
  .main-content .login-body.login-body-2 {
    padding: 30px;
  }
  .main-content .login-body.login-body-2 .top {
    height: 60px;
  }
  .main-content .login-body .top {
    height: 70px;
  }
  .main-content .login-body .bottom {
    padding: 30px;
  }
  .main-content .login-body .bottom .form-control {
    height: 40px;
  }
  .main-content .login-body .bottom .other-option .social-box.gap-20 {
    gap: 15px;
  }
  .main-content .login-body .bottom .other-option .social-box a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    font-size: 16px;
  }
  .compose-mail.expanded .ck-content {
    height: 350px;
  }
}
@media only screen and (max-width: 1199px) and (min-width: 992px) {
  /* ======== Global Responsive Styles ======== */
  body {
    overflow-x: hidden;
  }
  body.body-padding {
    padding-left: 0;
  }
  body.body-p-top {
    padding-top: 60px;
  }
  body.body-p-top.has-horizontal {
    padding-top: 109px;
  }
  body.has-two-column-menu {
    padding-left: 60px;
  }
  body .main-content {
    overflow: hidden;
  }
  .row {
    --bs-gutter-y: 15px;
    --bs-gutter-x: 15px;
  }
  .gap-3 {
    gap: 10px !important;
  }
  .form-check:not(.form-switch) .form-check-input {
    width: 14px;
    height: 14px;
  }
  .form-check label {
    font-size: 12px;
    line-height: 14px;
  }
  .form-check label input {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  .table {
    font-size: 12px;
  }
  .mb-25 {
    margin-bottom: 15px;
  }
  .panel.chart-panel-1 .panel-body {
    padding-right: 5px;
    padding-left: 0;
    margin-left: -3px;
  }
  .panel .panel-header {
    height: 50px;
    padding: 0 15px;
  }
  .panel .panel-header h5 {
    font-size: 16px;
    line-height: 50px;
  }
  .panel .panel-body {
    padding: 15px;
  }
  .btn-flush {
    font-size: 14px;
  }
  .date-box {
    width: 40px;
    height: 40px;
  }
  .date-box span {
    font-size: 12px;
  }
  .fs-14 {
    font-size: 12px;
  }
  /* ======== Header Section Responsive ======== */
  .header {
    height: 60px;
  }
  .header .gap-20 {
    gap: 10px;
  }
  .main-logo {
    width: 230px;
    height: 60px;
  }
  .nav-close-btn button {
    font-size: 22px;
  }
  .site-view-btn.btn {
    padding: 0;
    line-height: 100%;
    background: transparent !important;
    border: 0 !important;
    font-size: 20px;
    color: #c8d4f0;
  }
  .site-view-btn.btn i {
    margin-right: 0 !important;
  }
  .site-view-btn.btn span {
    display: none;
  }
  .site-view-btn.btn:hover, .site-view-btn.btn:focus {
    background-color: transparent !important;
    -webkit-box-shadow: 0 0 !important;
            box-shadow: 0 0 !important;
  }
  .header-right-btns {
    gap: 10px;
    padding-right: 10px;
  }
  .header-right-btns .header-form:focus-within input {
    width: 200px;
  }
  .header-right-btns .lang-select {
    font-size: 14px;
  }
  .header-btn {
    font-size: 20px;
  }
  .light-theme .site-view-btn.btn {
    color: #464646;
  }
  /* ======== Right Sidebar Section Responsive ======== */
  .right-sidebar .right-sidebar-group#navBarSizeGroup {
    display: none !important;
  }
  /* ======== Main Sidebar Section Responsive ======== */
  .main-sidebar {
    width: 230px;
    padding-top: 60px;
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
    -webkit-transition: all 0.3s ease-in;
    transition: all 0.3s ease-in;
  }
  .main-sidebar::after {
    width: 230px;
    border-right: 1px solid rgba(255, 255, 255, 0.15);
  }
  .main-sidebar.sidebar-mini {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  .main-sidebar.collapsed {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  .main-sidebar.collapsed.two-column-menu {
    width: 230px;
    padding-top: 60px;
    -webkit-transition: none;
    transition: none;
  }
  .main-sidebar.collapsed.two-column-menu.sub-menu-collapsed {
    width: 60px;
  }
  .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu[id] {
    width: 180px;
    padding: 5px 0;
  }
  .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link {
    padding: 5px 10px;
    font-size: 13px;
  }
  .main-sidebar.collapsed .main-menu {
    padding: 5px 1px 5px 5px;
  }
  .main-sidebar.collapsed .os-content {
    gap: 5px;
  }
  .main-sidebar.collapsed .sidebar-menu {
    padding-right: 4px;
  }
  .main-sidebar.collapsed .sidebar-menu .os-scrollbar-vertical {
    width: 3px;
  }
  .main-sidebar.collapsed .sidebar-item {
    width: 50px;
  }
  .main-sidebar.collapsed .sidebar-item .sidebar-link {
    padding: 5px;
    line-height: 2;
    text-align: center;
    height: 40px;
  }
  .main-sidebar.collapsed .sidebar-item .sidebar-link .nav-icon {
    width: auto;
  }
  .main-sidebar.horizontal-menu {
    top: 60px;
    -webkit-transform: translate(0);
            transform: translate(0);
  }
  .main-menu {
    padding: 10px 0 10px 10px;
  }
  .sidebar-link-group {
    padding: 0;
  }
  .sidebar-item {
    font-size: 13px;
    font-weight: 400;
    margin-bottom: 10px;
  }
  .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active {
    font-weight: 500;
  }
  .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active::after {
    width: 8px;
  }
  .sidebar-menu .help-center {
    padding: 20px 10px;
  }
  .sidebar-menu .help-center h3 {
    font-size: 16px;
    margin-top: -3px;
    margin-bottom: 14px;
  }
  .sidebar-menu .help-center p {
    font-size: 12px;
    margin-bottom: 15px;
  }
  .light-theme .main-sidebar::after {
    border-color: rgba(0, 0, 0, 0.1);
  }
  /* ======== Main Content Section Responsive ======== */
  .main-content {
    padding: 15px 15px 60px;
  }
  .main-content.login-panel.login-panel-2 .panel-title {
    font-size: 25px;
    margin-bottom: 15px;
  }
  .main-content.login-panel.login-panel-3 .container {
    background-size: 300px;
  }
  .main-content .dashboard-breadcrumb.mb-25 {
    margin-bottom: 20px;
  }
  .main-content .dashboard-breadcrumb h2 {
    font-size: 20px;
    line-height: 17px;
  }
  .main-content .dashboard-top-box {
    padding: 10px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left {
    padding: 10px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left p {
    margin-top: -1px;
    margin-bottom: 3px !important;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left p.text-muted {
    margin-bottom: -3px !important;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left h3 {
    margin-bottom: 12px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .right {
    height: 100%;
    padding: 10px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .right .part-icon {
    width: 50px;
    height: 50px;
    line-height: 50px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .right .part-icon span {
    font-size: 20px;
  }
  .main-content .dashboard-top-box .left h3 {
    font-size: 14px;
    font-weight: 500;
    margin-top: -2px;
    margin-bottom: 7px;
  }
  .main-content .dashboard-top-box .left p {
    font-size: 12px;
  }
  .main-content .dashboard-top-box .left a {
    font-size: 10px;
  }
  .main-content .new-customer-table tr td {
    padding: 7.5px 0;
  }
  .main-content .new-customer-table .new-customer .part-img {
    width: 45px;
    height: 45px;
  }
  .main-content .recent-order-table {
    font-size: 12px;
    max-width: none;
    min-width: -webkit-max-content !important;
    min-width: -moz-max-content !important;
    min-width: max-content !important;
  }
  .main-content .recent-project-table {
    font-size: 12px;
  }
  .main-content .table-activity td {
    padding: 14.5px 10px;
  }
  .main-content .activity-box .part-txt span {
    font-size: 12px;
  }
  .main-content .activity-box .part-txt span:first-child {
    margin-bottom: 9px;
  }
  .main-content .pending-task-table td {
    padding: 10px 15px;
  }
  .main-content .pending-task-table .task-box span:first-child {
    margin-bottom: 7px;
  }
  .main-content .invoice-table th {
    padding: 10px 15px;
  }
  .main-content .invoice-table td {
    padding: 10px 15px;
  }
  .main-content .task-table td {
    padding: 10px 15px;
  }
  .main-content .notice-board-table th {
    padding: 10px 15px;
  }
  .main-content .notice-board-table td {
    padding: 10px 15px;
  }
  .main-content .deadline-table th {
    padding: 10px 15px;
  }
  .main-content .deadline-table td {
    padding: 10px 15px;
  }
  .main-content .table-bottom-control {
    padding: 15px;
  }
  .main-content .upcoming-interview li .avatar {
    width: 60px;
    height: 60px;
  }
  .main-content .jquery-uploader-preview-container .jquery-uploader-select-card,
  .main-content .jquery-uploader-preview-container .jquery-uploader-card {
    width: 160px;
    height: 160px;
  }
  .main-content .chatting-panel {
    width: calc(100% - 30px);
    height: calc(100% - 75px);
  }
  .main-content .chatting-panel .panel:first-child {
    width: 30%;
  }
  .main-content .chatting-panel .panel:nth-child(2) {
    width: 70%;
  }
  .main-content .chatting-panel .panel:last-child {
    position: absolute;
    right: 0;
    width: 30%;
    border: 0 !important;
    -webkit-transition: all 0.1s ease-in;
    transition: all 0.1s ease-in;
    z-index: 11;
  }
  .main-content .chatting-panel .panel:last-child.closed {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
  .main-content .chatting-panel .avatar {
    width: 30px;
    height: 30px;
  }
  .main-content .chatting-panel .message-list {
    height: calc(100% - 60px);
  }
  .main-content .chatting-panel .message-list .single-message {
    padding: 10px;
    margin-bottom: 10px;
  }
  .main-content .chatting-panel .message-list .single-message:last-child {
    margin-bottom: 0;
  }
  .main-content .chatting-panel .message-list .single-message.unread::after {
    left: 3px;
    width: 4px;
    height: 4px;
  }
  .main-content .chatting-panel .message-list .single-message .avatar {
    width: 30px;
    height: 30px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt {
    width: calc(100% - 40px);
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .top {
    margin-top: -1px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .user-name {
    font-size: 12px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .msg-time {
    font-size: 10px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .msg-short {
    font-size: 12px;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user {
    gap: 5px;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt {
    gap: 1px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .user-name {
    font-size: 14px;
    font-weight: 400;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .active-status {
    font-size: 10px;
    padding-left: 10px;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .active-status::after {
    width: 7px;
    height: 7px;
  }
  .main-content .chatting-panel .msg-area {
    height: calc(100% - 125px);
  }
  .main-content .chatting-panel .msg-area .single-message {
    max-width: 75%;
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner .sent-status {
    font-size: 10px;
  }
  .main-content .chatting-panel .msg-area .single-message .avatar {
    display: none;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box {
    max-width: 100%;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner {
    padding: 10px;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner:last-child::after {
    display: none;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner p {
    font-size: 12px;
    letter-spacing: 0;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option {
    left: calc(100% + 5px);
    gap: 7px;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option .msg-time {
    font-size: 10px;
  }
  .main-content .chatting-panel .msg-area .day-divider {
    margin-bottom: 8px;
  }
  .main-content .chatting-panel .msg-area .day-divider span {
    font-size: 12px;
  }
  .main-content .chatting-panel .msg-type-area form .chat-input {
    width: calc(100% - 80px);
  }
  .main-content .chatting-panel .user-short .back-to-chat-btn {
    position: absolute;
    top: 10px;
    left: 10px;
  }
  .main-content .chatting-panel .user-short .avatar {
    width: 50px;
    height: 50px;
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .user-short .part-txt {
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .user-short .part-txt span.user-name {
    font-size: 14px;
    margin-top: -1px;
    margin-bottom: 7px;
  }
  .main-content .chatting-panel .user-short .part-txt span.user-mail {
    font-size: 12px;
  }
  .main-content .chatting-panel .user-short .user-option {
    gap: 10px;
  }
  .main-content .chatting-panel .chatting-option {
    padding: 10px;
  }
  .main-content .chatting-panel .chatting-option.mb-20 {
    margin-bottom: 10px;
  }
  .main-content .chatting-panel .chatting-option ul li {
    font-size: 14px;
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .chatting-option ul li a span {
    margin-right: 5px;
  }
  .main-content .chatting-panel .chatting-panel-top-btns {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 15px;
  }
  .main-content .chatting-panel .chatting-panel-top-btns .btn.btn-icon {
    width: 25px;
    height: 25px;
    line-height: 25px;
    font-size: 14px;
  }
  .main-content .chatting-panel .chatting-panel-top-btns > .btn.btn-icon {
    width: auto;
    height: auto;
    border: 0;
    background: transparent;
  }
  .main-content .chatting-panel .chatting-panel-top-btns > .btn.btn-icon:active, .main-content .chatting-panel .chatting-panel-top-btns > .btn.btn-icon:focus {
    background-color: transparent !important;
    -webkit-box-shadow: 0 0;
            box-shadow: 0 0;
  }
  .main-content .email-panel {
    width: calc(100% - 30px);
    height: calc(100% - 75px);
  }
  .main-content .email-panel > .panel .panel-body:not(.email-menu) {
    height: calc(100% - 50px);
  }
  .main-content .email-panel .table-wrapper {
    height: calc(100% - 135px);
  }
  .main-content .email-panel .table-wrapper .dataTables_scrollBody {
    min-height: calc(100% - 48px);
  }
  .main-content .email-panel .tab-pane .tab-content.email-tab-content {
    height: calc(100% - 181px);
  }
  .main-content .email-panel .tab-pane .tab-content.email-tab-content .table-wrapper .dataTables_scrollBody {
    min-height: calc(100% - 94px);
  }
  .main-content .email-panel .inbox-tab {
    gap: 5px !important;
  }
  .main-content .email-panel .email-table {
    min-width: -webkit-max-content;
    min-width: -moz-max-content;
    min-width: max-content;
  }
  .main-content .email-panel .email-table td .table-txt, .main-content .email-panel .email-table th .table-txt {
    max-width: 250px;
  }
  .main-content .email-panel .mail-details {
    position: fixed;
    top: 70px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    width: calc(100% - 20px);
    background: #112143;
    border-radius: 3px;
    padding: 10px;
    z-index: 13;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-profile .part-txt span {
    font-size: 12px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-profile .part-txt span.mail {
    font-size: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-action {
    gap: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-action .date {
    font-size: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-action button {
    font-size: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-body p {
    font-size: 12px;
  }
  .main-content .email-panel .mail-body-top .right {
    display: none !important;
  }
  .main-content .email-panel .mail-menu-btn {
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  .main-content .profile-edit-tab-title h6 {
    font-size: 14px;
    margin-top: -1px;
    margin-bottom: 8px;
  }
  .main-content .profile-edit-tab .admin-profile .image-wrap {
    width: 100px;
    margin-bottom: 20px;
  }
  .main-content .profile-edit-tab .admin-profile .image-wrap .image-change {
    right: 10px;
    width: 25px;
    height: 25px;
    font-size: 12px;
  }
  .main-content .icon-row {
    gap: 15px;
  }
  .main-content .icon-row .icon-col {
    width: calc(16.6666666667% - 12.9px);
  }
  .main-content .file-manager-row {
    gap: 15px;
  }
  .main-content .file-manager-row .file-manager-col {
    width: calc(25% - 11.3px);
  }
  .main-content .file-card .part-img {
    height: 90px;
  }
  .main-content .steps-sidebar {
    width: 350px;
    padding: 50px 30px;
  }
  .main-content .steps-sidebar::before {
    left: 70px;
  }
  .main-content .steps-sidebar::after {
    width: calc(100% + 70px);
  }
  .main-content .all-steps {
    right: 30px;
    width: calc(100% - 380px);
    height: calc(100% - 140px);
    padding: 40px;
  }
  .main-content .error-panel {
    top: 15px;
    right: 15px;
    bottom: 15px;
    left: 15px;
  }
  .main-content .error-panel .part-img {
    max-height: 200px;
  }
  .main-content .error-panel .part-img img {
    max-height: 100%;
  }
  .main-content .error-panel .error-title {
    font-size: 70px;
  }
  .main-content .error-panel .error-subtitle {
    font-size: 25px;
  }
  .main-content .static-body {
    width: 450px;
  }
  .main-content .static-body .panel-body {
    padding: 25px;
  }
  .main-content .static-body .logo {
    margin-bottom: 20px;
  }
  .main-content .static-body .part-txt {
    margin-bottom: 15px;
  }
  .main-content .static-body .part-txt h2 {
    font-size: 20px;
    margin-bottom: 11px;
  }
  .main-content .static-body .part-txt p {
    margin-bottom: 19px;
  }
  .main-content .static-body .part-txt p.fs-5 {
    font-size: 16px !important;
  }
  .main-content .static-body .part-img {
    padding: 0 50px;
  }
  .main-content .static-body .part-img.mb-lg-5 {
    margin-bottom: 20px !important;
  }
  .main-content .static-body .otp-fake-input {
    width: 40px;
    height: 40px;
  }
  .main-content .static-body .verification-area #otp_target {
    margin-bottom: 10px;
  }
  .main-content .static-body .verification-area p {
    font-size: 14px;
    margin-bottom: 15px !important;
  }
  .main-content .login-body {
    width: 400px;
  }
  .main-content .login-body.login-body-2 {
    padding: 20px;
  }
  .main-content .login-body.login-body-2 .top {
    height: 50px;
    border-width: 2px;
  }
  .main-content .login-body.login-body-2 .bottom .form-control {
    height: 30px;
  }
  .main-content .login-body .top {
    height: 60px;
  }
  .main-content .login-body .bottom {
    padding: 20px 30px;
  }
  .main-content .login-body .bottom .panel-title {
    font-size: 18px;
    margin-bottom: 11px;
  }
  .main-content .login-body .bottom .login-status .msg-success.mb-20, .main-content .login-body .bottom .login-status .msg-error.mb-20 {
    margin-bottom: 10px !important;
  }
  .main-content .login-body .bottom .form-control {
    height: 35px;
  }
  .main-content .login-body .bottom .other-option .social-box.gap-20 {
    gap: 15px;
  }
  .main-content .login-body .bottom .other-option .social-box a {
    width: 35px;
    height: 35px;
    line-height: 35px;
    font-size: 16px;
  }
  .main-content .login-body .bottom .other-option p {
    font-size: 12px;
  }
  /* dark theme responsive design */
  .dark-theme .main-content .email-panel .mail-details {
    background: #242526;
  }
  .dark-theme .main-content .email-panel .mail-details .mail-reply-option {
    background: #242526;
  }
  .dark-theme .main-content .file-manager-sidebar-col {
    background: #242526;
  }
  /* light theme responsive design */
  .light-theme .main-content .email-panel .mail-details {
    background: #fff;
  }
  .light-theme .main-content .email-panel .mail-details .mail-reply-option {
    background: #fff;
  }
}
@media only screen and (max-width: 991px) and (min-width: 320px) {
  /* ======== Global Responsive Styles ======== */
  body {
    overflow-x: hidden;
  }
  body.body-padding {
    padding-left: 0;
  }
  body.body-p-top {
    padding-top: 50px;
  }
  body.has-two-column-menu {
    padding-left: 50px;
  }
  body .main-content {
    overflow: hidden;
  }
  small {
    font-size: 12px;
  }
  pre {
    font-size: 12px;
  }
  .select2-container--default .select2-selection--single .select2-selection__arrow {
    width: 15px;
  }
  .select2-container .form-control.form-control-sm {
    padding-right: 15px;
  }
  .select2-container--default .select2-selection--single .select2-selection__placeholder {
    font-size: 12px;
  }
  .select2-results__option {
    font-size: 12px;
  }
  .dataTables_length {
    font-size: 12px;
  }
  .fc-toolbar.fc-header-toolbar {
    margin-bottom: 10px;
  }
  .fc-toolbar h2 {
    font-size: 14px;
    font-weight: 400;
    line-height: 0.9;
  }
  .fc-unthemed th {
    font-size: 12px;
    font-weight: 500;
    padding: 5px;
  }
  .fc-dayGrid-view .fc-day-number,
  .fc-dayGrid-view .fc-week-number {
    font-size: 12px;
  }
  .fc-dayGrid-view .fc-body .fc-row {
    min-height: 60px;
  }
  .fc-day-grid-event {
    margin: 0 3px;
  }
  tr:first-child > td > .fc-day-grid-event {
    margin-top: 0;
  }
  .fc-day-grid-event {
    margin-bottom: 3px;
  }
  .fc-event, .fc-event-dot {
    font-size: 10px;
    padding: 1px 3px;
  }
  .fc-day-grid-event .fc-time {
    font-weight: 500;
  }
  .fc-unthemed td {
    font-size: 10px;
  }
  .sidebar-event-list .fc-event, .sidebar-event-list .fc-event-dot {
    font-size: 12px;
    padding: 3px 5px;
  }
  .nested-sortable-handle,
  .nested-sortable {
    gap: 3px;
  }
  .dropzone.dz-component .dz-message .dz-button {
    padding: 45px 0;
  }
  .dropzone.dz-component .dz-message .dz-button i {
    font-size: 30px;
    margin-top: -2px;
  }
  .dropzone.dz-component .dz-message .dz-button span {
    font-size: 12px;
  }
  .dropzone .dz-preview .dz-remove {
    opacity: 1;
  }
  .dataTables_wrapper .dataTables_length {
    float: left;
  }
  .dataTables_wrapper .dataTables_filter {
    float: right;
    margin-top: 0;
  }
  .dataTables_wrapper .dataTables_filter input {
    padding: 3px 8px;
  }
  .dataTables_wrapper .dataTables_info {
    float: left;
    font-size: 12px;
  }
  .dataTables_wrapper .dataTables_paginate {
    float: right;
    margin-top: 0;
  }
  .dataTables_wrapper .dataTables_paginate .btn {
    width: 15px;
    height: 15px;
    line-height: 14px;
    font-size: 10px;
  }
  .row {
    --bs-gutter-y: 10px;
    --bs-gutter-x: 10px;
  }
  .gap-3 {
    gap: 10px !important;
  }
  .btn {
    padding: 3.5px 10px;
    font-size: 14px;
  }
  .btn.btn-icon {
    width: 30px;
    height: 30px;
    line-height: 30px;
  }
  .btn-group-sm > .btn, .btn-sm {
    padding: 3px 8px;
    font-size: 12px;
  }
  .btn-group-sm > .btn.btn-icon, .btn-sm.btn-icon {
    width: 26px;
    height: 26px;
    line-height: 26px;
  }
  .form-control {
    padding: 5px 10px;
    font-size: 12px;
  }
  .form-control.form-control-lg {
    padding: 8px 10px;
    font-size: 14px;
  }
  .form-control.form-control-sm {
    padding: 3px 8px;
    min-height: auto;
  }
  .form-control::-webkit-input-placeholder {
    font-size: 12px;
  }
  .form-control::-moz-placeholder {
    font-size: 12px;
  }
  .form-control:-ms-input-placeholder {
    font-size: 12px;
  }
  .form-control::-ms-input-placeholder {
    font-size: 12px;
  }
  .form-control::placeholder {
    font-size: 12px;
  }
  .input-group-text {
    min-width: 30px;
    font-size: 12px;
  }
  .form-label {
    font-size: 12px;
    margin-top: -1px;
    margin-bottom: 8px;
  }
  .form-check:not(.form-switch) .form-check-input {
    width: 14px;
    height: 14px;
  }
  .form-check:not(.form-switch) .form-check-input:checked {
    background-size: 10px !important;
  }
  .form-check.form-switch .form-check-input {
    height: 20px;
    width: 40px;
    background-size: 14px;
    background-position-x: 2px;
  }
  .form-check.form-switch .form-check-input:checked {
    background-position-x: 22px;
  }
  .form-check label {
    font-size: 12px;
    line-height: 14px;
    padding-left: 6px;
  }
  .form-check label input {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  .table {
    font-size: 12px;
  }
  .badge {
    padding: 4px 8px;
  }
  .modal .modal-header {
    padding: 10px;
  }
  .modal .modal-header .modal-title {
    font-size: 16px;
  }
  .modal .modal-body {
    padding: 10px;
  }
  .modal .modal-footer {
    padding: 10px;
  }
  .card .card-header {
    padding: 5px 10px;
    font-size: 14px;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
  .card .card-header.gap-4 {
    gap: 5px !important;
  }
  .card .card-header .form-check .form-check-input {
    width: 12px;
    height: 12px;
  }
  .card .card-header .form-check label {
    font-size: 10px;
  }
  .card .card-body {
    padding: 10px;
  }
  .tooltip-inner {
    font-size: 12px;
    line-height: 1.2;
  }
  .col-form-label {
    padding-top: 0;
    padding-bottom: 0;
    line-height: 100%;
    font-size: 12px;
  }
  .list-group-item {
    font-size: 12px;
    line-height: 1.5;
    padding: 5px;
  }
  .mb-25 {
    margin-bottom: 10px;
  }
  .mb-15 {
    margin-bottom: 10px;
  }
  .avatar {
    width: 35px;
    height: 35px;
  }
  .panel.chart-panel-1 .panel-body {
    padding-right: 0;
    padding-left: 0;
    margin-left: -8px;
  }
  .panel .panel-header {
    padding: 0 10px;
    height: 45px;
  }
  .panel .panel-header h5 {
    font-size: 14px;
    line-height: 45px;
  }
  .panel .panel-body {
    padding: 10px;
  }
  .dropdown-menu {
    padding: 5px 0;
  }
  .dropdown-menu .dropdown-txt {
    padding-top: 16px;
    margin-bottom: 5px;
  }
  .dropdown-menu .dropdown-txt p {
    font-size: 14px;
  }
  .dropdown-menu .dropdown-txt span {
    font-size: 12px;
    margin: 0;
  }
  .dropdown-menu .dropdown-item {
    padding: 6px 10px;
    font-size: 12px;
  }
  .digi-dropdown .dropdown-menu li {
    padding: 6px 10px;
  }
  .btn-flush {
    font-size: 14px;
  }
  .btn-star {
    font-size: 12px;
  }
  .date-box {
    width: 40px;
    height: 40px;
  }
  .date-box.date-box-lg {
    width: 50px;
    height: 50px;
  }
  .date-box.date-box-lg span {
    font-size: 14px;
  }
  .date-box span {
    font-size: 12px;
  }
  .fs-14 {
    font-size: 12px;
  }
  .form-check.gap-4 {
    gap: 10px !important;
  }
  .form-group.p-3 {
    padding: 10px !important;
  }
  .input-group-with-icon input {
    height: 30px;
  }
  .icon-alert {
    min-width: 300px;
  }
  /* ======== Header Section Responsive ======== */
  .header {
    background: rgba(17, 33, 67, 0.8);
    height: 50px;
    padding-bottom: 0;
    -webkit-transition: none;
    transition: none;
  }
  .header.expanded-in-mobile {
    height: 130px;
    padding-bottom: 80px;
  }
  .header .gap-20 {
    gap: 15px;
  }
  .mobile-logo {
    max-width: 120px;
    height: 50px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    margin: auto;
  }
  .nav-close-btn button {
    font-size: 20px;
    padding-left: 10px;
  }
  .site-view-btn.btn {
    padding: 0;
    line-height: 100%;
    background: transparent !important;
    border: 0 !important;
    font-size: 16px;
    color: #c8d4f0;
  }
  .site-view-btn.btn i {
    margin-right: 0 !important;
  }
  .site-view-btn.btn span {
    display: none;
  }
  .site-view-btn.btn:hover, .site-view-btn.btn:focus {
    background-color: transparent !important;
    -webkit-box-shadow: 0 0 !important;
            box-shadow: 0 0 !important;
  }
  .header-right-btns {
    gap: 5px;
    padding-right: 10px;
  }
  .header-collapse-group {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 80px;
    border-top: 1px dashed rgba(223, 223, 223, 0.15);
    padding: 5px 10px !important;
    display: none;
  }
  .header-collapse-group .header-right-btns {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    gap: 10px;
  }
  .header-right-btns .header-form {
    width: 100%;
    background: transparent;
  }
  .header-right-btns .header-form input {
    width: calc(100% - 30px);
    padding: 0 15px;
    font-size: 12px;
  }
  .header-right-btns .header-form button {
    height: 30px;
  }
  .header-right-btns .header-form:focus-within input {
    width: calc(100% - 30px);
    border-color: rgba(255, 255, 255, 0.1);
  }
  .header-right-btns .lang-select {
    font-size: 14px;
  }
  .header-btn {
    height: 20px;
    line-height: 22px;
    font-size: 16px;
    padding: 0 4px;
  }
  .header-right-btns .header-btn-box .badge {
    top: 1px;
    right: 2px;
    font-size: 0;
    padding: 0;
    min-width: auto;
    width: 8px;
    height: 8px;
  }
  #btnFullscreen {
    display: none;
  }
  .header-right-btns .profile-btn {
    width: 25px;
    height: 25px;
  }
  .header-right-btns .header-btn-box .message-dropdown,
  .header-right-btns .header-btn-box .notification-dropdown {
    top: 27px !important;
    right: 0px !important;
    left: auto !important;
    width: 230px;
  }
  .header-right-btns .profile-btn.show::after {
    bottom: -16px;
  }
  .header-right-btns .header-btn-box .notification-dropdown a .avatar,
  .header-right-btns .header-btn-box .message-dropdown a .avatar {
    width: 30px;
    height: 30px;
    margin-right: 5px;
  }
  .header-right-btns .header-btn-box .notification-dropdown a .notification-txt,
  .header-right-btns .header-btn-box .message-dropdown a .msg-txt {
    width: calc(100% - 35px);
    font-size: 12px;
  }
  .header-right-btns .header-btn-box .message-dropdown a .msg-txt span.msg-short {
    -webkit-line-clamp: 1;
    margin-bottom: 4px;
  }
  .header-right-btns .header-btn-box .message-dropdown a .msg-txt span.time {
    font-size: 10px;
  }
  .header-right-btns .header-btn-box .notification-dropdown a {
    padding: 7px 10px;
  }
  .profile-right-sidebar {
    width: 290px;
  }
  .dark-theme .site-view-btn.btn {
    color: #e4e4e4;
  }
  .light-theme .site-view-btn.btn {
    color: #464646;
  }
  /* ======== Right Sidebar Section Responsive ======== */
  .right-sidebar {
    width: 290px;
  }
  .right-sidebar .sidebar-title {
    height: 35px;
    padding: 0 10px;
  }
  .right-sidebar .sidebar-title h3 {
    font-size: 14px;
    line-height: 35px;
  }
  .right-sidebar .sidebar-body {
    height: calc(100% - 35px);
    padding: 10px;
  }
  .right-sidebar .sidebar-subtitle {
    font-size: 12px;
    margin-bottom: 10px;
  }
  .right-sidebar .right-sidebar-group {
    margin-bottom: 10px;
  }
  .right-sidebar .right-sidebar-group#navBarSizeGroup {
    display: none !important;
  }
  .right-sidebar .right-sidebar-group:last-child {
    padding-bottom: 10px;
  }
  .right-sidebar .settings-row {
    gap: 5px;
  }
  .right-sidebar .settings-row .settings-col {
    width: calc(33.**********% - 3.4px);
  }
  .right-sidebar .settings-row-2 {
    gap: 5px;
  }
  .right-sidebar .color-palette {
    width: calc(20% - 4px);
  }
  .right-sidebar .color-palette span {
    height: 20px;
  }
  .right-sidebar .sidebar-bg-btn-box {
    gap: 5px;
  }
  .right-sidebar .sidebar-bg-btn-box button {
    width: 45px;
    height: 80px;
  }
  .right-sidebar .main-content-bg-btn-box {
    gap: 5px;
  }
  .right-sidebar .main-content-bg-btn-box button {
    width: calc(33.**********% - 3.4px);
  }
  /* ======== Main Sidebar Section Responsive ======== */
  .main-sidebar {
    width: 230px;
    padding-top: 0;
    margin-top: 50px;
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
    -webkit-transition: all 0.3s ease-in;
    transition: all 0.3s ease-in;
  }
  .main-sidebar::after {
    width: 230px;
    border-right: 1px solid rgba(255, 255, 255, 0.15);
  }
  .main-sidebar.sidebar-mini {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  .main-sidebar.collapsed {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  .main-sidebar.collapsed.two-column-menu {
    width: 230px;
    padding-top: 0;
    -webkit-transition: none;
    transition: none;
  }
  .main-sidebar.collapsed.two-column-menu.sub-menu-collapsed {
    width: 50px;
  }
  .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu[id] {
    width: 180px;
    padding: 5px 0;
  }
  .main-sidebar.collapsed.two-column-menu .sidebar-dropdown-menu .sidebar-link {
    padding: 5px 10px;
    font-size: 12px;
  }
  .main-sidebar.collapsed .main-menu {
    padding: 5px 1px 5px 5px;
  }
  .main-sidebar.collapsed .os-content {
    gap: 5px;
  }
  .main-sidebar.collapsed .sidebar-menu {
    padding-right: 4px;
  }
  .main-sidebar.collapsed .sidebar-menu .os-scrollbar-vertical {
    width: 3px;
  }
  .main-sidebar.collapsed .sidebar-item {
    width: 40px;
  }
  .main-sidebar.collapsed .sidebar-item .sidebar-link {
    padding: 5px;
    line-height: 2;
    text-align: center;
    height: 35px;
  }
  .main-sidebar.collapsed .sidebar-item .sidebar-link .nav-icon {
    font-size: 14px;
    width: auto;
  }
  .main-menu {
    padding: 10px 0 10px 10px;
  }
  .sidebar-link-group-title {
    padding: 5px 10px;
  }
  .sidebar-link-group-title:after {
    right: 10px;
  }
  .sidebar-link-group {
    padding: 0;
  }
  .sidebar-item {
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 10px;
  }
  .sidebar-item .sidebar-dropdown-item .sidebar-link {
    padding: 7.5px 10px;
  }
  .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active {
    font-weight: 500;
  }
  .sidebar-item.open .sidebar-dropdown-item .sidebar-link.active::after {
    width: 8px;
  }
  .sidebar-item .sidebar-link.has-sub:after {
    right: 15px;
  }
  .sidebar-item .sidebar-link .nav-icon {
    width: 16px;
  }
  .sidebar-menu .help-center {
    padding: 20px 10px;
  }
  .sidebar-menu .help-center h3 {
    font-size: 16px;
    margin-top: -3px;
    margin-bottom: 14px;
  }
  .sidebar-menu .help-center p {
    font-size: 12px;
    margin-bottom: 15px;
  }
  .light-theme .main-sidebar::after {
    border-color: rgba(0, 0, 0, 0.1);
  }
  /* ======== Main Content Section Responsive ======== */
  .main-content {
    padding: 10px;
    padding-bottom: 45px;
  }
  .main-content.login-panel.login-panel-3 .container {
    background-size: contain;
  }
  .main-content.two-factor-panel .logo {
    margin-bottom: 10px !important;
  }
  .main-content.two-factor-panel p {
    font-size: 12px;
    margin-bottom: 3px !important;
  }
  .main-content .dashboard-breadcrumb {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .main-content .dashboard-breadcrumb.mb-25 {
    margin-bottom: 20px;
  }
  .main-content .dashboard-breadcrumb h2 {
    font-size: 16px;
    margin-top: -4px;
    margin-bottom: -3px;
  }
  .main-content .dashboard-filter {
    width: 200px;
  }
  .main-content .dashboard-filter .form-control {
    width: calc(100% - 40px);
  }
  .main-content .dashboard-top-box {
    padding: 10px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left {
    padding: 10px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left p {
    margin-top: -1px;
    margin-bottom: 3px !important;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left p.text-muted {
    margin-bottom: -3px !important;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .left h3 {
    margin-bottom: 12px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .right {
    height: 100%;
    padding: 10px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .right .part-icon {
    width: 50px;
    height: 50px;
    line-height: 50px;
  }
  .main-content .dashboard-top-box.dashboard-top-box-2 .right .part-icon span {
    font-size: 20px;
  }
  .main-content .dashboard-top-box .left h3 {
    font-size: 14px;
    font-weight: 500;
    margin-top: -1px;
    margin-bottom: 12px;
  }
  .main-content .dashboard-top-box .left p {
    font-size: 12px;
    margin-bottom: 24px;
  }
  .main-content .visitor-table td span {
    font-size: 12px;
  }
  .main-content .new-customer-table tr td {
    padding: 7.5px 0;
  }
  .main-content .new-customer-table .new-customer {
    gap: 10px;
  }
  .main-content .new-customer-table .new-customer .part-img {
    width: 35px;
    height: 35px;
  }
  .main-content .new-customer-table .new-customer .part-txt {
    width: calc(100% - 45px);
  }
  .main-content .new-customer-table .new-customer .part-txt p {
    font-size: 14px;
    margin-top: -2px;
    margin-bottom: 7px;
  }
  .main-content .new-customer-table .new-customer .part-txt span {
    font-size: 12px;
  }
  .main-content .recent-order-table {
    font-size: 12px;
    max-width: none;
    min-width: -webkit-max-content !important;
    min-width: -moz-max-content !important;
    min-width: max-content !important;
  }
  .main-content .recent-order-table thead th {
    padding: 10px 25px 10px 10px;
    background-size: 10px;
  }
  .main-content .recent-order-table tr td {
    padding: 10px 15px 9px 10px;
  }
  .main-content .recent-order-table .badge {
    width: auto;
  }
  .main-content .recent-order-table .btn-box {
    gap: 10px;
  }
  .main-content .recent-order-table .btn-box button {
    font-size: 12px;
  }
  .main-content .table-filter-option {
    margin-bottom: 10px;
  }
  .main-content .table-bottom-control {
    padding: 10px;
  }
  .main-content .table-bottom-control .dataTables_info {
    font-size: 10px;
  }
  .main-content .table-bottom-control .dataTables_paginate {
    gap: 5px;
  }
  .main-content .table-bottom-control .dataTables_paginate span {
    gap: 5px;
  }
  .main-content .table-bottom-control .btn {
    width: 15px;
    height: 15px;
    line-height: 15px;
    font-size: 10px;
  }
  .main-content .table-bottom-control .btn.previous, .main-content .table-bottom-control .btn.next {
    width: auto;
  }
  .main-content .recent-project-table {
    font-size: 12px;
  }
  .main-content .table-activity td {
    padding: 10px;
  }
  .main-content .activity-box .part-txt span {
    font-size: 12px;
  }
  .main-content .activity-box .part-txt span:first-child {
    margin-bottom: 9px;
  }
  .main-content .pending-task-table td {
    padding: 10px;
  }
  .main-content .pending-task-table .task-box span:first-child {
    margin-bottom: 7px;
  }
  .main-content .invoice-table th {
    padding: 10px;
  }
  .main-content .invoice-table td {
    padding: 10px;
  }
  .main-content .task-table td {
    padding: 10px;
  }
  .main-content .notice-board-table th {
    padding: 5px 10px;
  }
  .main-content .notice-board-table td {
    padding: 10px;
  }
  .main-content .deadline-table th {
    padding: 5px 10px;
  }
  .main-content .deadline-table td {
    padding: 10px;
  }
  .main-content .hr-recent-activity {
    padding-left: 4px;
  }
  .main-content .hr-recent-activity li {
    padding-left: 11px;
    padding-bottom: 15px;
  }
  .main-content .hr-recent-activity li:before {
    top: 9px;
  }
  .main-content .hr-recent-activity li::after {
    top: -1px;
    width: 10px;
    height: 10px;
  }
  .main-content .hr-recent-activity li .activity-name {
    font-size: 12px;
    margin-top: -1px;
    margin-bottom: 5px;
  }
  .main-content .hr-recent-activity li .activity-short {
    font-size: 12px;
  }
  .main-content .hr-recent-activity li .activity-time {
    font-size: 12px;
    margin-top: -1px;
  }
  .main-content .hr-notice-board li {
    margin-bottom: 10px;
  }
  .main-content .digi-dataTable {
    font-size: 12px;
  }
  .main-content .digi-dataTable thead th {
    padding-top: 6px;
    padding-bottom: 6px;
  }
  .main-content .digi-dataTable tr td {
    padding: 5px 15px 5px 10px;
  }
  .main-content .digi-dataTable .btn-box {
    gap: 10px;
  }
  .main-content .digi-dataTable .btn-box button:not(.btn) {
    font-size: 14px;
  }
  .main-content .upcoming-interview li {
    margin-bottom: 10px;
    gap: 10px;
  }
  .main-content .upcoming-interview li .avatar {
    width: 50px;
    height: 50px;
  }
  .main-content .upcoming-interview li .part-txt span.applicant-name {
    font-size: 14px;
    margin-bottom: 7px;
  }
  .main-content .product-table-quantity.mb-20 {
    margin-bottom: 10px;
  }
  .main-content .product-table-quantity ul {
    font-size: 12px;
  }
  .main-content .product-table-quantity ul.top-action {
    gap: 15px;
  }
  .main-content .product-table-quantity ul.top-action li .btn-flush {
    font-size: 12px;
  }
  .main-content .product-image-upload .part-txt {
    padding-bottom: 2px;
    margin-bottom: 10px;
  }
  .main-content .product-image-upload .part-txt h5 {
    font-size: 14px;
  }
  .main-content .product-image-upload .part-txt h5 span {
    font-size: 12px;
  }
  .main-content .jquery-uploader-preview-container {
    gap: 5px;
  }
  .main-content .jquery-uploader-preview-container .jquery-uploader-select-card,
  .main-content .jquery-uploader-preview-container .jquery-uploader-card {
    width: 110px;
    height: 110px;
  }
  .main-content .jquery-uploader-preview-container .jquery-uploader-preview-action {
    top: 3px;
    left: 3px;
    opacity: 1;
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
    height: -webkit-max-content;
    height: -moz-max-content;
    height: max-content;
    background: transparent;
  }
  .main-content .jquery-uploader-preview-container .jquery-uploader-preview-action ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    gap: 5px;
  }
  .main-content .jquery-uploader-preview-container .jquery-uploader-preview-action ul li {
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    border-radius: 3px;
    background: #f00000;
  }
  .main-content .jquery-uploader-preview-container .jquery-uploader-preview-action ul li:hover {
    color: #fff;
  }
  .main-content .product-data-tab .input-additional-txt {
    font-size: 12px;
    line-height: 1.2;
  }
  .main-content .publish-date {
    row-gap: 0;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    margin-bottom: 11px;
  }
  .main-content .publish-date label:not([for]) {
    font-size: 12px;
  }
  .main-content .publish-date input {
    width: 145px;
    font-size: 12px;
    line-height: 100%;
  }
  .main-content .product-categories .form-check {
    margin-top: 15px;
  }
  .main-content .product-tag-area .all-tags .item .close-tag {
    opacity: 1;
  }
  .main-content .table-product-card .part-img {
    width: 45px;
    height: 45px;
  }
  .main-content .table-category-card .part-icon span {
    width: 40px;
    height: 40px;
    font-size: 25px;
  }
  .main-content .chatting-panel {
    width: calc(100% - 20px);
    height: calc(100% - 55px);
  }
  .main-content .chatting-panel .panel:first-child {
    position: absolute;
    width: 100%;
    left: 0;
    border: 0 !important;
    -webkit-transition: all 0.1s ease-in;
    transition: all 0.1s ease-in;
    z-index: 11;
  }
  .main-content .chatting-panel .panel:first-child.closed {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
  }
  .main-content .chatting-panel .panel:nth-child(2) {
    width: 100%;
  }
  .main-content .chatting-panel .panel:last-child {
    position: absolute;
    width: 100%;
    border: 0 !important;
    -webkit-transition: all 0.1s ease-in;
    transition: all 0.1s ease-in;
    z-index: 11;
  }
  .main-content .chatting-panel .panel:last-child.closed {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
  }
  .main-content .chatting-panel .avatar {
    width: 30px;
    height: 30px;
  }
  .main-content .chatting-panel .message-list {
    height: calc(100% - 50px);
  }
  .main-content .chatting-panel .message-list .single-message {
    padding: 10px;
    margin-bottom: 10px;
  }
  .main-content .chatting-panel .message-list .single-message:last-child {
    margin-bottom: 0;
  }
  .main-content .chatting-panel .message-list .single-message.unread::after {
    left: 3px;
    width: 4px;
    height: 4px;
  }
  .main-content .chatting-panel .message-list .single-message .avatar {
    width: 30px;
    height: 30px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt {
    width: calc(100% - 40px);
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .top {
    margin-top: -1px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .user-name {
    font-size: 12px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .msg-time {
    font-size: 10px;
  }
  .main-content .chatting-panel .message-list .single-message .part-txt .msg-short {
    font-size: 12px;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user {
    gap: 5px;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt {
    gap: 1px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .user-name {
    font-size: 14px;
    font-weight: 400;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .active-status {
    font-size: 10px;
    padding-left: 10px;
  }
  .main-content .chatting-panel .chatting-area .chat-top-bar .user .part-txt .active-status::after {
    width: 7px;
    height: 7px;
  }
  .main-content .chatting-panel .msg-area {
    height: calc(100% - 100px);
  }
  .main-content .chatting-panel .msg-area .single-message {
    max-width: 75%;
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .msg-area .single-message.outgoing .msg-box .msg-box-inner .sent-status {
    font-size: 10px;
  }
  .main-content .chatting-panel .msg-area .single-message .avatar {
    display: none;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box {
    max-width: 100%;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner {
    padding: 10px;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner:last-child::after {
    display: none;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner p {
    font-size: 12px;
    letter-spacing: 0;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option {
    left: calc(100% + 5px);
    gap: 7px;
  }
  .main-content .chatting-panel .msg-area .single-message .msg-box .msg-box-inner .msg-option .msg-time {
    font-size: 10px;
  }
  .main-content .chatting-panel .msg-area .day-divider {
    margin-bottom: 8px;
  }
  .main-content .chatting-panel .msg-area .day-divider span {
    font-size: 12px;
  }
  .main-content .chatting-panel .msg-type-area form .chat-input {
    width: calc(100% - 80px);
  }
  .main-content .chatting-panel .user-short .back-to-chat-btn {
    position: absolute;
    top: 10px;
    left: 10px;
  }
  .main-content .chatting-panel .user-short .avatar {
    width: 50px;
    height: 50px;
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .user-short .part-txt {
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .user-short .part-txt span.user-name {
    font-size: 14px;
    margin-top: -1px;
    margin-bottom: 7px;
  }
  .main-content .chatting-panel .user-short .part-txt span.user-mail {
    font-size: 12px;
  }
  .main-content .chatting-panel .user-short .user-option {
    gap: 10px;
  }
  .main-content .chatting-panel .chatting-option {
    padding: 10px;
  }
  .main-content .chatting-panel .chatting-option.mb-20 {
    margin-bottom: 10px;
  }
  .main-content .chatting-panel .chatting-option ul li {
    font-size: 14px;
    margin-bottom: 15px;
  }
  .main-content .chatting-panel .chatting-option ul li a span {
    margin-right: 5px;
  }
  .main-content .chatting-panel .chatting-panel-top-btns {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 10px;
  }
  .main-content .chatting-panel .chatting-panel-top-btns .btn.btn-icon {
    width: 25px;
    height: 25px;
    line-height: 25px;
    font-size: 12px;
  }
  .main-content .chatting-panel .chatting-panel-top-btns > .btn.btn-icon {
    width: auto;
    height: auto;
    border: 0;
    background: transparent;
  }
  .main-content .chatting-panel .chatting-panel-top-btns > .btn.btn-icon:active, .main-content .chatting-panel .chatting-panel-top-btns > .btn.btn-icon:focus {
    background-color: transparent !important;
    -webkit-box-shadow: 0 0;
            box-shadow: 0 0;
  }
  .main-content .email-panel {
    position: relative;
    width: 100%;
    height: auto;
  }
  .main-content .email-panel > .panel:first-child {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: auto;
    max-width: 220px;
    height: auto;
    background: #112143;
    z-index: 12;
  }
  .main-content .email-panel > .panel:nth-child(2) {
    width: 100%;
  }
  .main-content .email-panel .inbox-tab {
    gap: 5px !important;
  }
  .main-content .email-panel .email-table {
    min-width: -webkit-max-content;
    min-width: -moz-max-content;
    min-width: max-content;
  }
  .main-content .email-panel .email-table td .table-txt, .main-content .email-panel .email-table th .table-txt {
    max-width: 250px;
  }
  .main-content .email-panel .mail-details {
    position: fixed;
    top: 60px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    width: calc(100% - 20px);
    background: #112143;
    border-radius: 3px;
    padding: 10px;
    z-index: 13;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-profile .part-txt span {
    font-size: 12px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-profile .part-txt span.mail {
    font-size: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-action {
    gap: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-action .date {
    font-size: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-top .mail-action button {
    font-size: 10px;
  }
  .main-content .email-panel .mail-details .single-mail .mail-body p {
    font-size: 12px;
  }
  .main-content .email-panel .mail-body-top .right {
    display: none !important;
  }
  .main-content .email-panel .mail-menu-btn {
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  .main-content .invoice .invoice-header .shop-address {
    margin-bottom: 10px;
  }
  .main-content .invoice .invoice-header .shop-address .logo {
    width: 100px;
  }
  .main-content .invoice .invoice-header .shop-address .logo.mb-20 {
    margin-bottom: 15px;
  }
  .main-content .invoice .invoice-header .shop-address p {
    font-size: 12px;
    margin-bottom: 2px !important;
  }
  .main-content .invoice .invoice-header .shop-address p:first-child {
    margin-top: -5px;
  }
  .main-content .invoice .invoice-header .shop-address p:last-child {
    margin-bottom: -3px !important;
  }
  .main-content .invoice .info-card {
    margin-bottom: 10px;
  }
  .main-content .invoice .info-card h3 {
    font-size: 14px;
    margin-bottom: 7px;
  }
  .main-content .invoice .table th, .main-content .invoice .table td {
    padding: 5px;
    font-size: 12px;
  }
  .main-content .invoice .total-payment-area ul li {
    font-size: 14px;
  }
  .main-content .invoice .invoice-note {
    font-size: 12px;
    margin-top: -13px;
    margin-bottom: -5px !important;
  }
  .main-content .profile-sidebar .top {
    padding-top: 15px;
    padding-bottom: 25px;
    gap: 10px;
  }
  .main-content .profile-sidebar .top .part-img {
    width: 80px;
  }
  .main-content .profile-sidebar .top .image-wrap .image-change {
    right: 10px;
    width: 25px;
    height: 25px;
    font-size: 12px;
  }
  .main-content .profile-sidebar .top .part-txt {
    width: calc(100% - 90px);
  }
  .main-content .profile-sidebar .top .part-txt h4 {
    font-size: 14px;
    margin-bottom: 5px;
  }
  .main-content .profile-sidebar .top .part-txt span {
    font-size: 12px;
    margin-bottom: 8px;
  }
  .main-content .profile-sidebar .top .part-txt .admin-social {
    font-size: 12px;
  }
  .main-content .profile-sidebar .bottom ul {
    margin-bottom: 12px;
  }
  .main-content .profile-sidebar .bottom ul li {
    font-size: 12px;
    line-height: 25px;
  }
  .main-content .profile-sidebar .bottom p {
    font-size: 14px;
    line-height: 24px;
    margin-top: -12px;
  }
  .main-content .profile-sidebar-title {
    font-size: 16px;
    font-weight: 500;
  }
  .main-content .profile-sidebar-subtitle {
    font-size: 14px;
    margin-top: -2px;
    margin-bottom: 18px;
  }
  .main-content .profile-sidebar-subtitle::after {
    width: 660px;
  }
  .main-content .user-activity ul li {
    margin-bottom: 20px;
  }
  .main-content .user-activity ul li .left {
    max-width: 270px;
    padding-left: 15px;
    padding-right: 10px;
  }
  .main-content .user-activity ul li .left::after {
    left: 1px;
    width: 11px;
    height: 11px;
  }
  .main-content .user-activity-title {
    font-size: 12px;
    line-height: 1.2;
    margin-top: -2px;
    margin-bottom: 8px;
  }
  .main-content .user-activity-details {
    font-size: 12px;
    margin-bottom: 1px;
  }
  .main-content .user-activity-date {
    font-size: 10px;
    margin-bottom: -4px;
  }
  .main-content .user-activity-time {
    font-size: 10px;
    padding-left: 5px;
  }
  .main-content .profile-edit-tab-title h6 {
    font-size: 14px;
    margin-top: -1px;
    margin-bottom: 8px;
  }
  .main-content .profile-edit-tab .admin-profile .image-wrap {
    width: 80px;
    margin-bottom: 15px;
  }
  .main-content .profile-edit-tab .admin-profile .image-wrap .image-change {
    right: 10px;
    width: 25px;
    height: 25px;
    font-size: 12px;
  }
  .main-content .profile-edit-tab .admin-profile .admin-name {
    font-size: 14px;
    margin-bottom: 7px;
  }
  .main-content .profile-edit-tab .admin-profile .admin-role {
    font-size: 12px;
    margin-bottom: 3px;
  }
  .main-content .animation-card div:last-child {
    padding: 30px 0;
  }
  .main-content .animation-card div:last-child img {
    max-width: 150px;
  }
  .main-content .product-image-upload.product-image-upload-2 .jquery-uploader-select-card,
  .main-content .product-image-upload.product-image-upload-2 .jquery-uploader-card {
    width: 110px;
    height: 110px;
  }
  .main-content .icon-row {
    gap: 10px;
  }
  .main-content .icon-row .icon-col .icon-box .btn-flush {
    top: 5px;
    right: 5px;
    font-size: 10px;
    opacity: 0.5;
  }
  .main-content .icon-row .icon-col .icon-box span {
    font-size: 10px;
    line-height: 1.2;
  }
  .main-content .icon-row .icon-col .icon-box span.icon {
    font-size: 20px;
    margin-bottom: 12px;
  }
  .main-content .map-panel iframe {
    height: 250px;
  }
  .main-content .file-manager-sidebar-col {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 8px;
    width: 250px;
    background: #112143;
    border-right: 1px solid rgba(255, 255, 255, 0.15);
    overflow: auto;
    z-index: 2;
  }
  .main-content .file-manager-sidebar nav button {
    height: 30px;
    font-size: 12px;
  }
  .main-content .file-manager-sidebar nav button.all-files-tab {
    height: 35px;
    line-height: 35px;
    font-size: 14px;
  }
  .main-content .file-manager-sidebar nav button i {
    width: 16px;
    font-size: 14px;
  }
  .main-content .file-manager-sidebar .connected-app .file-manager-sidebar-title {
    font-size: 14px;
  }
  .main-content .file-manager-sidebar .connected-app li {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
  }
  .main-content .file-manager-sidebar .connected-app li i {
    width: 16px;
    font-size: 14px;
  }
  .main-content .file-manager-sidebar .progress-txt {
    margin-bottom: 5px;
  }
  .main-content .file-manager-sidebar .progress-txt .file-category-name i {
    font-size: 14px;
  }
  .main-content .file-manager-sidebar .progress-txt .file-category-name p {
    font-size: 12px;
  }
  .main-content .file-manager-sidebar .progress-txt .using-storage {
    font-size: 12px;
  }
  .main-content .file-manager-sidebar .file-category-status li {
    margin-bottom: 15px;
  }
  .main-content .file-manager-menu-btn {
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  .main-content .file-manager-card {
    padding: 10px;
  }
  .main-content .file-manager-card .top .part-icon {
    font-size: 16px;
  }
  .main-content .file-manager-card .top .action {
    font-size: 16px;
  }
  .main-content .file-manager-card .top .dropdown-menu {
    min-width: 120px;
  }
  .main-content .file-manager-card .top .dropdown-menu .dropdown-item {
    padding: 6px 8px;
  }
  .main-content .file-manager-card .bottom {
    padding-top: 15px;
  }
  .main-content .file-manager-card .bottom span {
    font-size: 12px;
  }
  .main-content .file-manager-card .bottom .left .folder-name {
    font-size: 12px;
  }
  .main-content .file-manager-row {
    gap: 10px;
  }
  .main-content .file-manager-row.list-view {
    min-width: -webkit-max-content;
    min-width: -moz-max-content;
    min-width: max-content;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .main-content .file-manager-row.list-view .file-manager-col .file-card {
    padding: 5px 10px;
  }
  .main-content .file-manager-row.list-view .file-manager-col .file-card .part-img {
    margin-right: 80px;
  }
  .main-content .file-manager-row.list-view .file-manager-col .file-card .part-txt {
    width: 260px;
  }
  .main-content .file-manager-row.list-view .file-manager-col .file-card .file-type {
    width: 130px;
  }
  .main-content .file-manager-row.list-view .file-manager-col .file-card .action {
    width: 130px;
  }
  .main-content .file-manager-row .file-manager-col-head {
    padding: 9px 10px;
  }
  .main-content .file-manager-row .file-manager-col-head span {
    font-size: 12px;
  }
  .main-content .file-manager-row .file-manager-col-head span:nth-child(1) {
    width: 100px;
  }
  .main-content .file-manager-row .file-manager-col-head span:nth-child(2), .main-content .file-manager-row .file-manager-col-head span:nth-child(3), .main-content .file-manager-row .file-manager-col-head span:nth-child(4), .main-content .file-manager-row .file-manager-col-head span:nth-child(5) {
    width: 130px;
  }
  .main-content .file-card .action .btn-flush {
    font-size: 12px;
  }
  .main-content .file-card .action .dropdown-menu {
    min-width: 120px;
  }
  .main-content .file-card .action .dropdown-menu .dropdown-item {
    padding: 6px 8px;
  }
  .main-content .file-card .part-img .file-icon {
    width: 30px;
  }
  .main-content .file-card .part-txt .file-name {
    font-size: 10px;
    height: 14px;
  }
  .main-content .file-card .part-txt .file-size {
    font-size: 10px;
  }
  .main-content .file-card .file-type {
    font-size: 10px;
  }
  .main-content .part-btn {
    padding-top: 10px;
  }
  .main-content .part-btn p {
    font-size: 12px;
    margin-bottom: 8px;
  }
  .main-content .file-manager-user {
    padding: 15px 0 14px;
  }
  .main-content .file-manager-user .avatar {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
  }
  .main-content .file-manager-user .part-txt .name {
    margin-bottom: -5px;
  }
  .main-content .file-manager-user .part-txt span {
    font-size: 14px;
  }
  .main-content .file-manager-user .part-txt .email span {
    font-size: 12px;
  }
  .main-content .file-manager-user .part-txt .btn-flush {
    font-size: 12px;
  }
  .main-content .file-manager-settings-subtitle {
    margin-bottom: 10px;
  }
  .main-content .file-manager-settings-subtitle h6 {
    font-size: 14px;
    margin-top: -2px;
    margin-bottom: 2px;
  }
  .main-content .about-plan {
    margin-bottom: 15px;
  }
  .main-content .plan-name {
    font-size: 16px;
    margin-bottom: 12px;
  }
  .main-content .used-storage {
    font-size: 12px;
  }
  .main-content .file-manager-settings-table {
    margin-top: -9px;
  }
  .main-content .file-manager-settings-table td {
    padding: 5px;
    font-size: 12px;
  }
  .main-content .file-manager-settings-table .btn-flush {
    font-size: 12px;
  }
  .main-content .panel-header form.file-search input {
    max-width: 170px;
  }
  .main-content .login-body {
    width: 100%;
    max-width: 350px;
  }
  .main-content .login-body.login-body-2 {
    padding: 15px;
  }
  .main-content .login-body.login-body-2 .top {
    border-width: 2px !important;
    height: 50px;
    padding: 0 10px;
    margin-bottom: 20px;
  }
  .main-content .login-body .top {
    height: 50px;
    padding: 0 15px;
  }
  .main-content .login-body .top .logo {
    max-width: 100px;
  }
  .main-content .login-body .top a {
    font-size: 14px;
  }
  .main-content .login-body .bottom {
    padding: 15px;
  }
  .main-content .login-body .bottom .panel-title {
    font-size: 16px;
    margin-top: -1px;
    margin-bottom: 12px;
  }
  .main-content .login-body .bottom .input-group-text {
    width: 35px;
  }
  .main-content .login-body .bottom .form-control {
    height: 35px;
    font-size: 12px;
  }
  .main-content .login-body .bottom .other-option {
    padding-top: 15px;
  }
  .main-content .login-body .bottom .other-option p {
    font-size: 12px;
    margin-top: -1px;
    margin-bottom: 13px;
  }
  .main-content .login-body .bottom .other-option .social-box a {
    width: 30px;
    height: 30px;
    line-height: 31px;
    font-size: 14px;
  }
  .main-content .login-body .gap-20 {
    gap: 10px;
  }
  .main-content .static-body {
    width: 100%;
    max-width: 400px;
    margin: auto;
  }
  .main-content .static-body .panel-body {
    padding: 20px;
  }
  .main-content .static-body .logo {
    margin-bottom: 20px;
  }
  .main-content .static-body .part-txt {
    margin-bottom: 15px;
  }
  .main-content .static-body .part-txt h2 {
    font-size: 20px;
    margin-bottom: 11px;
  }
  .main-content .static-body .part-txt p {
    margin-bottom: 19px;
  }
  .main-content .static-body .part-txt p.fs-5 {
    font-size: 16px !important;
  }
  .main-content .static-body .part-img {
    padding: 0 !important;
    text-align: center;
  }
  .main-content .static-body .part-img.mb-lg-5 {
    margin-bottom: 20px !important;
  }
  .main-content .static-body .part-img img {
    max-height: 60px;
  }
  .main-content .static-body .otp-fake-input {
    width: 40px;
    height: 40px;
    padding: 5px;
  }
  .main-content .static-body .verification-area #otp_target {
    margin-bottom: 10px;
  }
  .main-content .static-body .verification-area p {
    font-size: 14px;
    margin-bottom: 15px !important;
  }
  .main-content .steps-sidebar {
    position: relative;
    width: 100%;
    height: auto;
    padding: 25px;
  }
  .main-content .steps-sidebar::before {
    display: none;
  }
  .main-content .steps-sidebar::after {
    width: 100%;
    background-size: 100%;
    background-position-y: top;
  }
  .main-content .steps-sidebar .sidebar-logo {
    text-align: center;
    margin-bottom: 20px;
  }
  .main-content .steps-sidebar .sidebar-content {
    height: -webkit-max-content;
    height: -moz-max-content;
    height: max-content;
  }
  .main-content .steps-sidebar .sidebar-content .step-list .os-content {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    gap: 10px;
  }
  .main-content .steps-sidebar .sidebar-content .step-list .os-content::before, .main-content .steps-sidebar .sidebar-content .step-list .os-content::after {
    display: none;
  }
  .main-content .steps-sidebar .sidebar-content .step-list li {
    display: block;
    margin-bottom: 0;
  }
  .main-content .steps-sidebar .sidebar-content .step-list li::after {
    top: 50%;
    left: 100%;
    right: auto;
    width: 15px;
    height: 0;
  }
  .main-content .steps-sidebar .sidebar-content .step-list li.completed .step-number::after {
    line-height: 28px;
  }
  .main-content .steps-sidebar .sidebar-content .step-list .step-txt {
    position: absolute;
    display: none;
  }
  .main-content .steps-sidebar .sidebar-content .step-list .step-number {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
  }
  .main-content .all-steps {
    position: static;
    -webkit-transform: inherit;
            transform: inherit;
    width: 100%;
    height: calc(100% - 135px);
    padding: 20px;
  }
  .main-content .all-steps .os-padding {
    margin-left: -5px;
    margin-right: -5px;
  }
  .main-content .all-steps .os-content {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
  .main-content .all-steps .step-content-wrap {
    width: auto;
  }
  .main-content .all-steps .step-content-wrap .step-content {
    margin-bottom: 30px;
  }
  .main-content .all-steps .step-heading {
    margin-bottom: 18px;
  }
  .main-content .all-steps .step-heading .step-title {
    font-size: 16px;
    margin-bottom: 5px;
  }
  .main-content .all-steps .step-heading span {
    font-size: 12px;
  }
  .main-content .all-steps .account-types .form-check {
    max-width: none;
  }
  .main-content .all-steps .account-types .form-check label {
    width: 100%;
    padding: 15px;
    gap: 10px;
  }
  .main-content .all-steps .account-types .form-check label .part-txt {
    width: calc(100% - 40px);
  }
  .main-content .all-steps .account-types .form-check label .title {
    font-size: 14px;
    margin-bottom: 7px;
  }
  .main-content .all-steps .account-types .form-check label .dscr {
    font-size: 12px;
  }
  .main-content .all-steps .account-types.account-plan .form-check label .title {
    font-size: 14px;
  }
  .main-content .all-steps .team-size .form-check label {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }
  .main-content .all-steps .billing-details-wrap {
    width: auto;
  }
  .main-content.login-panel.login-panel-2 .panel-title {
    font-size: 20px;
    padding-bottom: 10px;
    margin-top: -3px;
    margin-bottom: 20px;
  }
  .main-content.login-panel.login-panel-2 .panel-title::after {
    width: 35px;
  }
  .main-content.two-factor-panel {
    padding-left: 0;
    padding-right: 0;
  }
  .main-content.two-factor-panel .static-body {
    max-width: 400px;
  }
  .main-content.two-factor-panel .static-body .panel-body {
    padding: 30px 20px;
  }
  .main-content .coming-soon-panel .countdown {
    gap: 5px;
  }
  .main-content .coming-soon-panel .countdown li {
    font-size: 16px;
  }
  .main-content .coming-soon-panel .countdown li span {
    min-width: 55px;
    padding: 10px 5px;
  }
  .main-content .coming-soon-panel .countdown li p {
    font-size: 12px;
    padding-top: 10px;
  }
  .main-content .pricing-panel .panel-body {
    padding: 30px 0;
  }
  .main-content .pricing-panel .pricing {
    width: 100%;
  }
  .main-content .pricing-panel .pricing .table-wrap {
    padding-top: 40px;
  }
  .main-content .pricing-panel .pricing .table {
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content;
    max-width: 900px;
  }
  .main-content .pricing-panel .pricing .table thead th:first-child {
    padding: 15px;
  }
  .main-content .pricing-panel .pricing .table thead th:nth-child(4) .table-top::after {
    font-size: 16px;
    height: 35px;
    line-height: 33px;
  }
  .main-content .pricing-panel .pricing .table thead th .table-title h2 {
    font-size: 20px;
    line-height: 1.3;
  }
  .main-content .pricing-panel .pricing .table thead th .table-top {
    padding: 15px;
  }
  .main-content .pricing-panel .pricing .table thead th .table-top h3 {
    font-size: 16px;
    margin-bottom: 10px;
  }
  .main-content .pricing-panel .pricing .table thead th .table-top h2 {
    font-size: 18px;
  }
  .main-content .pricing-panel .pricing .table thead th .table-top h2 .type {
    font-size: 12px;
  }
  .main-content .pricing-panel .pricing .table thead th .table-top p {
    font-size: 14px;
    margin-bottom: 15px;
  }
  .main-content .pricing-panel .pricing .table tbody tr:not(.table-secondary) td:first-child {
    font-size: 12px;
  }
  .main-content .pricing-panel .pricing .table tbody tr td {
    padding: 5px;
  }
  .main-content .pricing-panel .pricing .table tbody tr td:first-child {
    font-size: 14px;
  }
  .main-content .pricing-panel .pricing .table tbody tr td .icon {
    font-size: 16px;
  }
  .main-content .error-panel {
    top: 10px;
    right: 10px;
    bottom: 10px;
    left: 10px;
  }
  .main-content .error-panel .part-img {
    max-height: 200px;
  }
  .main-content .error-panel .part-img img {
    max-height: 100%;
  }
  .main-content .error-panel .error-title {
    font-size: 50px;
  }
  .main-content .error-panel .error-subtitle {
    font-size: 18px;
  }
  .footer {
    height: 35px;
  }
  .footer p {
    font-size: 10px;
    line-height: 35px;
  }
  .modal .ck-content,
  .main-content .ck-content,
  .compose-mail .ck-content,
  .mail-details .ck-content {
    height: 200px;
    font-size: 12px;
  }
  .compose-mail .panel {
    width: calc(100% - 20px);
  }
  .view-task .task-created {
    font-size: 12px;
    margin-bottom: 15px;
  }
  .view-task h6 {
    font-size: 14px;
    margin-bottom: 8px;
  }
  .view-task .task-dscr p {
    font-size: 12px;
    margin-bottom: 10px;
  }
  .view-task .task-dscr .task-attachment {
    gap: 5px;
  }
  .view-task .task-dscr .task-attachment .attched-box {
    width: 100px;
    padding: 3px;
  }
  .view-task .comments .single-comment .comment-delete {
    top: 40px;
    left: 17.5px;
    opacity: 1;
  }
  .view-task .comments .single-comment .part-txt {
    width: calc(100% - 45px);
  }
  .view-task .comments .single-comment .part-txt p {
    font-size: 12px;
    margin-bottom: -5px;
  }
  .view-task .task-info-box ul li {
    font-size: 12px;
  }
  .voice-call {
    padding: 10px 0;
  }
  .voice-call::after {
    left: -11px;
    right: -11px;
    bottom: -11px;
    height: 40px;
  }
  .voice-call .user {
    margin-bottom: 20px;
  }
  .voice-call .user .avatar {
    margin-bottom: 15px;
  }
  .voice-call .user .user-name {
    font-size: 16px;
    margin-top: -1px;
    margin-bottom: 10px;
  }
  .voice-call .user .call-status {
    font-size: 12px;
  }
  .voice-call .call-option {
    gap: 5px;
    padding: 7px;
  }
  .call-modal {
    width: 280px;
    margin-left: auto;
    margin-right: auto;
  }
  .video-call .user .user-preview {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .video-call .user .user-preview img {
    max-width: none;
    max-height: 100%;
  }
  .video-call .user .part-txt .user-name {
    font-size: 16px;
    margin-top: -1px;
    margin-bottom: 1px;
  }
  .video-call .user .part-txt .call-status {
    font-size: 12px;
  }
  .video-call .bottom {
    padding-top: 10px;
    height: 54px;
  }
  .video-call .bottom .call-option {
    gap: 5px;
    padding: 7px;
  }
  .video-call .bottom .admin-preview {
    background: rgba(0, 0, 0, 0.55);
    width: 80px;
    margin-right: 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .video-call .bottom .admin-preview img {
    max-width: none;
    max-height: 100%;
  }
  .file-details-modal .part-img {
    max-width: 250px;
    margin: auto;
  }
  .file-details-modal .file-details li {
    font-size: 14px;
    margin-top: -2px;
    margin-bottom: 13px;
  }
  /* dark theme responsive design */
  .dark-theme .main-content .email-panel > .panel:first-child {
    background: #242526;
  }
  .dark-theme .main-content .email-panel .mail-details {
    background: #242526;
  }
  .dark-theme .main-content .email-panel .mail-details .mail-reply-option {
    background: #242526;
  }
  .dark-theme .main-content .file-manager-sidebar-col {
    background: #242526;
  }
  /* light theme responsive design */
  .light-theme .main-content .email-panel > .panel:first-child {
    background: #fff;
  }
  .light-theme .main-content .mail-details {
    background: #fff;
  }
  .light-theme .main-content .mail-details .mail-reply-option {
    background: #fff;
  }
  .light-theme .main-content .file-manager-sidebar-col {
    background: #fff;
    border-color: rgba(0, 0, 0, 0.1);
  }
}
@media only screen and (max-width: 991px) and (min-width: 576px) {
  .calendar-sidebar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    gap: 10px;
  }
  .calendar-sidebar .panel {
    width: calc(50% - 5px);
    margin-bottom: 0;
  }
  .header.expanded-in-mobile {
    height: 93px;
    padding-bottom: 43px;
  }
  .header-collapse-group {
    height: 43px;
  }
  .header-collapse-group .header-right-btns {
    -webkit-box-pack: justify !important;
        -ms-flex-pack: justify !important;
            justify-content: space-between !important;
  }
  .header-collapse-group .header-right-btns .header-form {
    float: left;
    width: 250px;
  }
  .main-content .dashboard-top-box {
    padding: 15px;
  }
  .main-content .digi-dataTable thead th {
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .main-content .digi-dataTable tr td {
    padding: 10px 15px 10px 10px;
  }
  .main-content .chatting-panel {
    width: calc(100% - 20px);
    height: calc(100% - 55px);
  }
  .main-content .chatting-panel .panel:first-child {
    position: static;
    width: 35%;
  }
  .main-content .chatting-panel .panel:nth-child(2) {
    width: 65%;
  }
  .main-content .chatting-panel .panel:last-child {
    width: 40%;
    right: 0;
  }
  .main-content .email-panel .email-table td .table-txt, .main-content .email-panel .email-table th .table-txt {
    max-width: 500px;
  }
  .main-content .icon-row .icon-col {
    width: calc(25% - 7.5px);
  }
  .main-content .file-manager-row .file-manager-col {
    width: calc(25% - 7.5px);
  }
  .main-content .file-card .part-img {
    height: 80px;
  }
}
@media only screen and (max-width: 991px) and (min-width: 768px) {
  .main-content .add-product-sidebar {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    -webkit-column-gap: 10px;
       -moz-column-gap: 10px;
            column-gap: 10px;
  }
  .main-content .add-product-sidebar .panel {
    width: calc(50% - 5px);
  }
  .main-content .icon-row .icon-col {
    width: calc(20% - 8px);
  }
  .main-content .file-manager-row.list-view .file-manager-col-head > * {
    width: 20% !important;
  }
  .main-content .file-manager-row.list-view .file-manager-col .file-card > * {
    width: 20% !important;
  }
  .main-content .file-manager-row.list-view .file-manager-col .file-card > *:nth-child(2) {
    width: 40% !important;
  }
  .main-content .file-manager-row.list-view .file-manager-col .file-card .part-img {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    margin-right: auto;
  }
  .main-content .file-manager-row.list-view .file-manager-col .file-card .part-img .btn-flush {
    width: 20px;
  }
}
@media only screen and (max-width: 767px) and (min-width: 320px) {
  .main-content.login-panel.login-panel-3 .container {
    background: transparent;
  }
  .main-content.login-panel.login-panel-3 .container .justify-content-end {
    -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
            justify-content: center !important;
  }
}
@media only screen and (max-width: 575px) and (min-width: 320px) {
  .main-content .icon-row .icon-col {
    width: calc(25% - 7.5px);
  }
  .main-content .file-manager-row .file-manager-col {
    width: calc(33.**********% - 6.7px);
  }
  .main-content .file-card .part-img {
    height: 80px;
  }
  #minimizeComposeMail {
    display: none;
  }
}
@media only screen and (max-width: 479px) and (min-width: 320px) {
  .fc-toolbar.fc-header-toolbar {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 10px;
  }
  .col-xs-1 {
    width: 8.**********%;
  }
  .col-xs-2 {
    width: 16.6666666667%;
  }
  .col-xs-3 {
    width: 25%;
  }
  .col-xs-4 {
    width: 33.**********%;
  }
  .col-xs-5 {
    width: 41.6666666667%;
  }
  .col-xs-6 {
    width: 50%;
  }
  .col-xs-7 {
    width: 58.**********%;
  }
  .col-xs-8 {
    width: 66.6666666667%;
  }
  .col-xs-9 {
    width: 75%;
  }
  .col-xs-10 {
    width: 83.**********%;
  }
  .col-xs-11 {
    width: 91.6666666667%;
  }
  .col-xs-12 {
    width: 100%;
  }
  .panel .panel-header {
    padding: 10px;
    height: auto;
    min-height: 45px;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    gap: 10px;
  }
  .panel .panel-header h5 {
    line-height: 1;
    margin-top: -1px;
    margin-bottom: -2px;
  }
  /* ======== Main Content Section Responsive ======== */
  .main-content .dashboard-breadcrumb {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 10px;
  }
  .main-content .dataTables_filter {
    max-width: 150px;
  }
  .main-content .product-table-quantity ul.top-action li:first-child {
    display: none;
  }
  .main-content .user-activity ul li .left {
    max-width: 220px;
  }
  .main-content .icon-row .icon-col {
    width: calc(33.**********% - 6.7px);
  }
  .main-content .file-manager-row .file-manager-col {
    width: calc(50% - 5px);
  }
  .main-content .file-card .part-img {
    height: 80px;
  }
  .main-content .all-steps .account-types .col-6 {
    width: 100%;
  }
}