<div class="modal" id="<?php echo e($formMode === 'edit' ? 'edit-experience-modal' : ''); ?>">
        <div class="modal-content">
            <h2><?php echo e($formMode === 'edit' ? 'Edit Experience' : 'Create Experience'); ?></h2>
            <span class="close-modal">×</span>
            <hr>
            <div>
                <label>Company</label>
                <?php echo $errors->first('company', '<p class="error">:message</p>'); ?>

                <input type="text" name="company" id="<?php echo e($formMode === 'edit' ? 'edit-experience-company' : ''); ?>" value="<?php echo e(isset($experience->company) ? $experience->company : ''); ?>"/>

                <label>Period</label>
                <?php echo $errors->first('period', '<p class="error">:message</p>'); ?>

                <input type="text" name="period" id="<?php echo e($formMode === 'edit' ? 'edit-experience-period' : ''); ?>" value="<?php echo e(isset($experience->period) ? $experience->period : ''); ?>"/>

                <label>Position</label>
                <?php echo $errors->first('position', '<p class="error">:message</p>'); ?>

                <input type="text" name="position" id="<?php echo e($formMode === 'edit' ? 'edit-experience-position' : ''); ?>" value="<?php echo e(isset($experience->position) ? $experience->position : ''); ?>"/>
            </div>
            <hr>
            <div class="modal-footer">
                <button type="button" class="close-modal">Cancel</button>
                <button type="submit" class="secondary">
                    <span><i class="fa fa-spinner fa-spin"></i></span>
                    <?php echo e($formMode === 'edit' ? 'Update' : 'Save'); ?>

                </button>
            </div>
        </div>
    </div><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\LaraFolio\resources\views/admin/experiences/form.blade.php ENDPATH**/ ?>