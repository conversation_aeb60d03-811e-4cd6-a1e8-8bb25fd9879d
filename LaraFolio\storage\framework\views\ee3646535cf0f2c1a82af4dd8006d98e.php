
<?php $__env->startSection('content'); ?>
<section class="projects" id="projects">
    <div class="titlebar">
        <h1>Projects </h1>
        <a href="<?php echo e(route('admin.projects.create')); ?>" class="open-modal">
            <button class="btn-icon success">New Project</button>
        </a>        
    </div>
    <?php echo $__env->make('includes.flash_message', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="table">

        <div class="table-filter">
            <div>
                <ul class="table-filter-list">
                    <li>
                        <p class="table-filter-link link-active">All</p>
                    </li>
                </ul>
            </div>
        </div>
        <div class="table-search">
            <div>
                <select class="search-select" name="" id="">
                    <option value="">Filter Project</option>
                </select>
            </div>
            <div class="relative">
                <input class="search-input" type="text" name="search" placeholder="Search Project...">
            </div>
        </div>

        <div class="project_table-heading">
            <p>Image</p> 
            <p>Title</p>
            <p>Description</p>
            <p>Link</p>
            <p>Actions</p>
        </div>
        <!-- item 1 -->
         <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="project_table-items">
                <p>
                    <?php if($project->image): ?>
                        <img src="<?php echo e(asset('assets/img/' . $project->image)); ?>" alt="" class="project_img-list">
                    <?php else: ?>
                        <img src="<?php echo e(asset('assets/img/no-image.png')); ?>" alt="" class="project_img-list">
                    <?php endif; ?>
                </p>
                <p><?php echo e($project->title); ?></p>
                <p><?php echo e($project->description); ?></p>
                <p><?php echo e($project->link); ?></p>
                <div>
                    <a href="<?php echo e(route('admin.projects.edit', $project->id)); ?>" class="open-modal">
                    <button class="btn-icon success edit-experience-btn"
                            data-id="<?php echo e($project->id); ?>"
                            data-title="<?php echo e($project->title); ?>"
                            data-description="<?php echo e($project->description); ?>"
                            data-link="<?php echo e($project->link); ?>">
                        <i class="fas fa-pencil-alt"></i>
                    </button>
                    </a>                    
                    <form method="POST" action="<?php echo e(route('admin.projects.destroy', $project->id)); ?>" style="display: inline;" class="delete-form">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="button" class="btn-icon danger delete-btn" data-experience-name="<?php echo e($project->title); ?>">
                            <i class="far fa-trash-alt"></i>
                        </button>
                    </form>
                </div>
            </div>
         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
        <div class="table-paginate">
            <div class="pagination">
                <a href="#" class="btn">&laquo;</a>
                <a href="#" class="btn active">1</a>
                <a href="#" class="btn">2</a>
                <a href="#" class="btn">3</a>
                <a href="#" class="btn">&raquo;</a>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\LaraFolio\resources\views/admin/projects/index.blade.php ENDPATH**/ ?>