
<?php $__env->startSection('content'); ?>
<section class="testimonials" id="projects">
    <div class="titlebar">
        <h1>Testimonials </h1>
        <a href="<?php echo e(route('admin.testimonials.create')); ?>" class="open-modal">
            <button class="btn-icon success open--modal success">New Testimonial</button>            
        </a>
    </div>
    <?php echo $__env->make('includes.flash_message', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="table">

        <div class="table-filter">
            <div>
                <ul class="table-filter-list">
                    <li>
                        <p class="table-filter-link link-active">All</p>
                    </li>
                </ul>
            </div>
        </div>
        <div class="table-search">
            <div>
                <select class="search-select" name="" id="">
                    <option value="">Filter Project</option>
                </select>
            </div>
            <div class="relative">
                <input class="search-input" type="text" name="search" placeholder="Search Project...">
            </div>
        </div>

        <div class="testimonial_table-heading">
            <p>Photo</p> 
            <p>name</p>
            <p>Function</p>
            <p>Testimony</p>
            <p>Rating</p>
            <p>Actions</p>
        </div>
        <!-- item 1 -->
         <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="testimonial_table-items">
                <p>
                    <?php if($testimonial->image): ?>
                        <img src="<?php echo e(asset('assets/img/' . $testimonial->image)); ?>" alt="" class="testimonial_img-list">
                    <?php else: ?>
                        <img src="<?php echo e(asset('assets/img/no-image.png')); ?>" alt="" class="testimonial_img-list">
                    <?php endif; ?>
                </p>
                <p><?php echo e($testimonial->name); ?></p>
                <p><?php echo e($testimonial->function); ?></p>
                <p><?php echo e($testimonial->testimony); ?></p>
                <p><?php echo e($testimonial->rating); ?>/5</p>
                <div>
                    <a href="<?php echo e(route('admin.testimonials.edit', $testimonial->id)); ?>" class="open-modal">
                    <button class="btn-icon success edit-experience-btn"
                            data-id="<?php echo e($testimonial->id); ?>"
                            data-name="<?php echo e($testimonial->name); ?>"
                            data-function="<?php echo e($testimonial->function); ?>"
                            data-testimony="<?php echo e($testimonial->testimony); ?>"
                            data-rating="<?php echo e($testimonial->rating); ?>">
                        <i class="fas fa-pencil-alt"></i>
                    </button>
                    </a>                    
                    <form method="POST" action="<?php echo e(route('admin.testimonials.destroy', $testimonial->id)); ?>" style="display: inline;" class="delete-form">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="button" class="btn-icon danger delete-btn" data-experience-name="<?php echo e($testimonial->name); ?>">
                            <i class="far fa-trash-alt"></i>
                        </button>
                    </form>
                </div>
                <!-- <div>
                    <button class="btn-icon success">
                        <i class="fas fa-pencil-alt"></i>
                    </button>
                    <button class="btn-icon danger" >
                        <i class="far fa-trash-alt"></i>
                    </button>
                </div> -->
            </div>
         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        
        <div class="table-paginate">
            <div class="pagination">
                <a href="#" class="btn">&laquo;</a>
                <a href="#" class="btn active">1</a>
                <a href="#" class="btn">2</a>
                <a href="#" class="btn">3</a>
                <a href="#" class="btn">&raquo;</a>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\LaraFolio\resources\views/admin/testimonials/index.blade.php ENDPATH**/ ?>