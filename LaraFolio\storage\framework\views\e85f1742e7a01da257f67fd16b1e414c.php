<?php $__env->startSection('content'); ?>
<section class="experiences" id="experiences">
    <div class="titlebar">
        <h1>Experiences </h1>
        <button class="btn-icon success open-modal">New Experience</button>
    </div>
    <?php echo $__env->make('admin.experiences.create', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('includes.flash_message', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Hidden input to detect validation errors for create form -->
    <?php if($errors->any() && !request()->has('edit')): ?>
        <input type="hidden" id="has-create-errors" value="1">
    <?php endif; ?>
    <div class="table">
        <div class="table-filter">
            <div>
                <ul class="table-filter-list">
                    <li>
                        <p class="table-filter-link link-active">All</p>
                    </li>
                </ul>
            </div>
        </div>
        <div class="table-search">
            <div>
                <select class="search-select" name="" id="">
                    <option value="">Filter Experience</option>
                </select>
            </div>
            <div class="relative">
                <input class="search-input" type="text" name="search" placeholder="Search Experience...">
            </div>
        </div>
        <div class="experience_table-heading">
            <p>Company</p> 
            <p>Period</p>
            <p>Position</p>
            <p>Actions</p>
        </div>
        <!-- item 1 -->
         <?php $__currentLoopData = $experiences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $experience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="experience_table-items">
                <p><?php echo e($experience->company); ?></p>
                <p><?php echo e($experience->period); ?></p>
                <p><?php echo e($experience->position); ?></p>
                <div>
                    <button class="btn-icon success edit-experience-btn"
                            data-id="<?php echo e($experience->id); ?>"
                            data-company="<?php echo e($experience->company); ?>"
                            data-period="<?php echo e($experience->period); ?>"
                            data-position="<?php echo e($experience->position); ?>">
                        <i class="fas fa-pencil-alt"></i>
                    </button>
                    <form method="POST" action="<?php echo e(route('admin.experiences.destroy', $experience->id)); ?>" style="display: inline;" class="delete-form">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="button" class="btn-icon danger delete-btn" data-experience-name="<?php echo e($experience->company); ?>">
                            <i class="far fa-trash-alt"></i>
                        </button>
                    </form>
                </div>
            </div>
            <?php echo $__env->make('admin.experiences.edit', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    </div>
</section>

<!-- Edit Experience Modal -->
<div id="edit-experience-modal" style="display: none;">
    <?php
        $editExperience = new App\Models\Experience();
        $editExperience->id = 0; // Placeholder ID that will be updated by JavaScript
    ?>
    <?php echo $__env->make('admin.experiences.edit', ['experience' => $editExperience], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\LaraFolio\resources\views/admin/experiences/index.blade.php ENDPATH**/ ?>