
<?php $__env->startSection('content'); ?>
<section class="educations" id="educations">
    <div class="titlebar">
        <h1>Educations </h1>
        <button class="btn-icon success open-modal">New Education</button>
    </div>
    <?php echo $__env->make('admin.educations.create', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('includes.flash_message', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Hidden input to detect validation errors for create form -->
    <?php if($errors->any() && !request()->has('edit')): ?>
        <input type="hidden" id="has-create-errors" value="1">
    <?php endif; ?>
    <div class="table">
        <div class="table-filter">
            <div>
                <ul class="table-filter-list">
                    <li>
                        <p class="table-filter-link link-active">All</p>
                    </li>
                </ul>
            </div>
        </div>
        <div class="table-search">
            <div>
                <select class="search-select" name="" id="">
                    <option value="">Filter Service</option>
                </select>
            </div>
            <div class="relative">
                <input class="search-input" type="text" name="search" placeholder="Search Service...">
            </div>
        </div>

        <div class="education_table-heading">
            <p>Institution</p> 
            <p>Period</p>
            <p>Degree</p>
            <p>Department</p>
            <p>Actions</p>
        </div>
        <!-- item 1 -->
         <?php $__currentLoopData = $educations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $education): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="education_table-items">
                <p><?php echo e($education->institution); ?></p>
                <p><?php echo e($education->period); ?></p>
                <p><?php echo e($education->degree); ?></p>
                <p><?php echo e($education->department); ?></p>
                <div>
                    <button class="btn-icon success edit-education-btn"
                            data-id="<?php echo e($education->id); ?>"
                            data-institution="<?php echo e($education->institution); ?>"
                            data-period="<?php echo e($education->period); ?>"
                            data-degree="<?php echo e($education->degree); ?>"
                            data-department="<?php echo e($education->department); ?>">
                        <i class="fas fa-pencil-alt"></i>
                    </button>
                    <form method="POST" action="<?php echo e(route('admin.educations.destroy', $education->id)); ?>" style="display: inline;" class="delete-form">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="button" class="btn-icon danger delete-btn" data-education-name="<?php echo e($education->institution); ?>">
                            <i class="far fa-trash-alt"></i>
                        </button>
                    </form>
                </div>
            </div>
            <?php echo $__env->make('admin.educations.edit', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\LaraFolio\resources\views/admin/educations/index.blade.php ENDPATH**/ ?>